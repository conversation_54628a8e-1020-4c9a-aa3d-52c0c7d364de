package main

import (
	"archive/zip"
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func main() {
	fmt.Println("🚀 测试Metro2自动生成功能...")

	// 测试ZIP文件创建
	testZipFileCreation()

	// 测试邮件内容生成
	testEmailContentGeneration()

	// 测试配置文件读取
	testConfigFileReading()

	fmt.Println("🎉 测试完成！")
}

func testZipFileCreation() {
	fmt.Println("\n📦 测试ZIP文件创建...")

	// 模拟Metro2文件内容
	metro2Content := []byte("HEADER|TEST|Metro2|File|Content")
	jsonContent := []byte(`{"header": {"test": "data"}, "data": [{"test": "record"}]}`)

	// 创建ZIP文件
	var buf bytes.Buffer
	zipWriter := zip.NewWriter(&buf)

	// 添加Metro2文件
	metro2Writer, err := zipWriter.Create("RM-Metro2-2025-01-20250131-120000.txt")
	if err != nil {
		fmt.Printf("❌ 创建Metro2文件失败: %v\n", err)
		return
	}
	_, err = metro2Writer.Write(metro2Content)
	if err != nil {
		fmt.Printf("❌ 写入Metro2文件失败: %v\n", err)
		return
	}

	// 添加JSON文件
	jsonWriter, err := zipWriter.Create("RM-Metro2-Backup-2025-01-20250131-120000.json")
	if err != nil {
		fmt.Printf("❌ 创建JSON文件失败: %v\n", err)
		return
	}
	_, err = jsonWriter.Write(jsonContent)
	if err != nil {
		fmt.Printf("❌ 写入JSON文件失败: %v\n", err)
		return
	}

	// 关闭ZIP writer
	err = zipWriter.Close()
	if err != nil {
		fmt.Printf("❌ 关闭ZIP文件失败: %v\n", err)
		return
	}

	zipData := buf.Bytes()
	fmt.Printf("✅ ZIP文件创建成功，大小: %d 字节\n", len(zipData))

	// 保存到临时文件进行验证
	tempDir := os.TempDir()
	testZipPath := filepath.Join(tempDir, "test-metro2-report.zip")
	err = os.WriteFile(testZipPath, zipData, 0644)
	if err != nil {
		fmt.Printf("❌ 保存ZIP文件失败: %v\n", err)
		return
	}
	defer os.Remove(testZipPath)

	fmt.Printf("✅ ZIP文件已保存到: %s\n", testZipPath)
}

func testEmailContentGeneration() {
	fmt.Println("\n📧 测试邮件内容生成...")

	recipientEmail := "<EMAIL>"
	reportMonth := time.Now()

	// 创建测试用的generation log
	testGenerationLog := struct {
		TotalLeases   int
		TotalTenants  int
		TotalPayments int
	}{
		TotalLeases:   5,
		TotalTenants:  8,
		TotalPayments: 12,
	}

	fmt.Printf("  - 收件人: %s\n", recipientEmail)
	fmt.Printf("  - 报告月份: %s\n", reportMonth.Format("2006-01"))
	fmt.Printf("  - 测试数据: %d个租约, %d个租户, %d笔支付\n",
		testGenerationLog.TotalLeases,
		testGenerationLog.TotalTenants,
		testGenerationLog.TotalPayments)

	// 测试邮件内容生成
	subject := fmt.Sprintf("Metro2 Monthly Report - %s", reportMonth.Format("January 2006"))
	fmt.Printf("  - 邮件主题: %s\n", subject)

	// 测试HTML内容生成
	htmlBody := fmt.Sprintf(`
		<h2>Metro2 Monthly Report - %s</h2>
		<p>Dear Administrator,</p>
		<p>The Metro2 monthly report has been automatically generated and is attached to this email.</p>
		<h3>Report Summary:</h3>
		<ul>
			<li><strong>Report Month:</strong> %s</li>
			<li><strong>Generated At:</strong> %s</li>
			<li><strong>Total Leases Processed:</strong> %d</li>
			<li><strong>Total Tenants:</strong> %d</li>
			<li><strong>Total Payments:</strong> %d</li>
		</ul>
		<h3>Files Included:</h3>
		<ul>
			<li>Metro2 Report File (.txt) - Standard Metro2 format for credit bureau submission</li>
			<li>JSON Backup File (.json) - Detailed data backup for internal records</li>
		</ul>
		<p>Best regards,<br/>Report Rentals System</p>
	`,
		reportMonth.Format("January 2006"),
		reportMonth.Format("2006-01"),
		time.Now().Format("2006-01-02 15:04:05 MST"),
		testGenerationLog.TotalLeases,
		testGenerationLog.TotalTenants,
		testGenerationLog.TotalPayments)

	fmt.Printf("  - HTML内容长度: %d 字符\n", len(htmlBody))

	// 测试文件路径
	jsonFilePath := "/uploads/metro2_reports/RM-Metro2-Backup-2025-07-20250731-120000.json"
	metro2FilePath := "/uploads/metro2_reports/RM-Metro2-2025-07-20250731-120000.txt"

	fmt.Printf("  - JSON文件路径: %s\n", jsonFilePath)
	fmt.Printf("  - Metro2文件路径: %s\n", metro2FilePath)

	fmt.Println("✅ 邮件内容生成测试完成")
}

func testConfigFileReading() {
	fmt.Println("\n📋 测试配置文件读取...")

	// 检查local.ini文件是否存在
	configPath := "src/local.ini"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("❌ 配置文件不存在: %s\n", configPath)
		return
	}

	// 读取配置文件内容
	content, err := os.ReadFile(configPath)
	if err != nil {
		fmt.Printf("❌ 读取配置文件失败: %v\n", err)
		return
	}

	configContent := string(content)
	fmt.Printf("✅ 配置文件读取成功，大小: %d 字节\n", len(configContent))

	// 检查Metro2自动生成配置段是否存在
	if strings.Contains(configContent, "[metro2AutoGeneration]") {
		fmt.Println("✅ 找到Metro2自动生成配置段")

		// 检查各个配置项
		configs := []string{
			"enabled",
			"sendDay",
			"sendHour",
			"recipientEmail",
			"processIntervalMinutes",
			"testMode",
		}

		for _, config := range configs {
			if strings.Contains(configContent, config+" =") {
				fmt.Printf("  ✅ 找到配置项: %s\n", config)
			} else {
				fmt.Printf("  ❌ 缺少配置项: %s\n", config)
			}
		}

		// 检查邮箱地址
		if strings.Contains(configContent, "<EMAIL>") {
			fmt.Println("  ✅ 邮箱地址配置正确")
		} else {
			fmt.Println("  ❌ 邮箱地址配置不正确")
		}

	} else {
		fmt.Println("❌ 未找到Metro2自动生成配置段")
	}
}
