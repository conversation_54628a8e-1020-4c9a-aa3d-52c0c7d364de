package main

import (
	"fmt"
	"time"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID           string
	Status       string
	AutoPay      bool
	StartDate    string
	EndDate      string
	RentDueDay   int
	RentAmount   float64
	OwingBalance float64
}

// 模拟查询条件检查
func shouldIncludeInProcessing(lease TestLease, today time.Time) bool {
	todayStr := today.Format("2006-01-02")
	
	// 检查状态
	if lease.Status != "active" {
		return false
	}
	
	// 检查开始日期
	if lease.StartDate != "" && lease.StartDate > todayStr {
		return false
	}
	
	// 检查结束日期
	if lease.EndDate != "" && lease.EndDate < todayStr {
		return false
	}
	
	return true
}

// 模拟 getActiveLeasesForProcessing 函数
func getActiveLeasesForProcessing(leases []TestLease, today time.Time) []TestLease {
	var result []TestLease
	
	for _, lease := range leases {
		if shouldIncludeInProcessing(lease, today) {
			result = append(result, lease)
		}
	}
	
	return result
}

// TestScenario10Stage1 测试阶段1：扩展查询逻辑
func TestScenario10Stage1() {
	fmt.Println("=== 测试 Scenario 10 阶段1：扩展查询逻辑 ===")
	
	today := time.Now()
	fmt.Printf("当前日期: %s\n\n", today.Format("2006-01-02"))

	// 创建测试租约数据
	testLeases := []TestLease{
		{
			ID:           "lease-1",
			Status:       "active",
			AutoPay:      true,
			StartDate:    "2025-01-01",
			EndDate:      "",
			RentDueDay:   5,
			RentAmount:   1000.0,
			OwingBalance: 1200.0,
		},
		{
			ID:           "lease-2",
			Status:       "active",
			AutoPay:      false, // 关键：autoPay=false 的租约
			StartDate:    "2025-02-01",
			EndDate:      "",
			RentDueDay:   15,
			RentAmount:   800.0,
			OwingBalance: 800.0,
		},
		{
			ID:           "lease-3",
			Status:       "ended",
			AutoPay:      true,
			StartDate:    "2024-01-01",
			EndDate:      "2024-12-31",
			RentDueDay:   1,
			RentAmount:   1200.0,
			OwingBalance: 0.0,
		},
		{
			ID:           "lease-4",
			Status:       "active",
			AutoPay:      false,
			StartDate:    "2025-08-01", // 未来开始
			EndDate:      "",
			RentDueDay:   10,
			RentAmount:   900.0,
			OwingBalance: 0.0,
		},
		{
			ID:           "lease-5",
			Status:       "pending",
			AutoPay:      true,
			StartDate:    "2025-01-01",
			EndDate:      "",
			RentDueDay:   20,
			RentAmount:   1100.0,
			OwingBalance: 0.0,
		},
	}

	fmt.Printf("=== 测试查询逻辑变更 ===\n")
	
	// 模拟旧的查询逻辑（只包含 autoPay=true）
	fmt.Printf("旧逻辑 (autoPay=true only):\n")
	oldResults := []TestLease{}
	for _, lease := range testLeases {
		if lease.Status == "active" && lease.AutoPay && shouldIncludeInProcessing(lease, today) {
			oldResults = append(oldResults, lease)
		}
	}
	
	fmt.Printf("  找到 %d 个租约:\n", len(oldResults))
	for _, lease := range oldResults {
		fmt.Printf("    - %s (autoPay=%t, status=%s)\n", lease.ID, lease.AutoPay, lease.Status)
	}

	// 模拟新的查询逻辑（包含所有 active 租约）
	fmt.Printf("\n新逻辑 (all active leases):\n")
	newResults := getActiveLeasesForProcessing(testLeases, today)
	
	fmt.Printf("  找到 %d 个租约:\n", len(newResults))
	for _, lease := range newResults {
		fmt.Printf("    - %s (autoPay=%t, status=%s)\n", lease.ID, lease.AutoPay, lease.Status)
	}

	// 验证结果
	fmt.Printf("\n=== 验证结果 ===\n")
	
	// 检查是否包含了 autoPay=false 的租约
	hasAutoPayFalse := false
	hasAutoPayTrue := false
	
	for _, lease := range newResults {
		if lease.AutoPay {
			hasAutoPayTrue = true
		} else {
			hasAutoPayFalse = true
		}
	}
	
	fmt.Printf("包含 autoPay=true 的租约: %t\n", hasAutoPayTrue)
	fmt.Printf("包含 autoPay=false 的租约: %t\n", hasAutoPayFalse)
	
	if hasAutoPayTrue && hasAutoPayFalse {
		fmt.Printf("✅ 查询逻辑扩展成功\n")
	} else if hasAutoPayTrue && !hasAutoPayFalse {
		fmt.Printf("⚠️  只找到 autoPay=true 的租约\n")
	} else {
		fmt.Printf("❌ 查询逻辑有问题\n")
	}

	// 检查过滤条件
	fmt.Printf("\n=== 过滤条件测试 ===\n")
	
	filterTests := []struct {
		name     string
		lease    TestLease
		expected bool
		reason   string
	}{
		{
			name: "Active + AutoPay=true",
			lease: TestLease{
				ID: "test-1", Status: "active", AutoPay: true,
				StartDate: "2025-01-01", EndDate: "",
			},
			expected: true,
			reason:   "应该被包含",
		},
		{
			name: "Active + AutoPay=false",
			lease: TestLease{
				ID: "test-2", Status: "active", AutoPay: false,
				StartDate: "2025-01-01", EndDate: "",
			},
			expected: true,
			reason:   "应该被包含（新逻辑）",
		},
		{
			name: "Ended lease",
			lease: TestLease{
				ID: "test-3", Status: "ended", AutoPay: true,
				StartDate: "2024-01-01", EndDate: "2024-12-31",
			},
			expected: false,
			reason:   "状态不是active",
		},
		{
			name: "Future lease",
			lease: TestLease{
				ID: "test-4", Status: "active", AutoPay: true,
				StartDate: "2025-12-01", EndDate: "",
			},
			expected: false,
			reason:   "开始日期在未来",
		},
		{
			name: "Pending lease",
			lease: TestLease{
				ID: "test-5", Status: "pending", AutoPay: true,
				StartDate: "2025-01-01", EndDate: "",
			},
			expected: false,
			reason:   "状态不是active",
		},
	}

	for i, test := range filterTests {
		result := shouldIncludeInProcessing(test.lease, today)
		fmt.Printf("测试 %d: %s\n", i+1, test.name)
		fmt.Printf("  结果: %t (预期: %t)\n", result, test.expected)
		fmt.Printf("  原因: %s\n", test.reason)
		
		if result == test.expected {
			fmt.Printf("  ✅ 通过\n")
		} else {
			fmt.Printf("  ❌ 失败\n")
		}
		fmt.Println()
	}

	// 统计信息
	fmt.Printf("=== 统计信息 ===\n")
	fmt.Printf("总租约数: %d\n", len(testLeases))
	fmt.Printf("旧逻辑匹配: %d\n", len(oldResults))
	fmt.Printf("新逻辑匹配: %d\n", len(newResults))
	fmt.Printf("新增处理: %d\n", len(newResults)-len(oldResults))

	fmt.Printf("\n=== 阶段1测试完成 ===\n")
}

func main() {
	TestScenario10Stage1()
}
