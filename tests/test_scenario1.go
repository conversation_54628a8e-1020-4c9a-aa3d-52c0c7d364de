package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	RentAmount            float64
	AdditionalMonthlyFees float64
	OwingBalance          float64
}

// TestScenario1 测试创建租约时的初始余额设置
func TestScenario1() {
	fmt.Println("=== 测试 Scenario 1: Create Lease 初始余额设置 ===")

	// 创建测试租约
	lease := &TestLease{
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          0.0, // 初始为0
	}

	fmt.Printf("创建租约前:\n")
	fmt.Printf("  RentAmount: $%.2f\n", lease.RentAmount)
	fmt.Printf("  AdditionalMonthlyFees: $%.2f\n", lease.AdditionalMonthlyFees)
	fmt.Printf("  OwingBalance: $%.2f\n", lease.OwingBalance)

	// 模拟创建租约时的余额设置逻辑
	// (不实际调用数据库，只测试逻辑)
	lease.OwingBalance = lease.RentAmount + lease.AdditionalMonthlyFees

	fmt.Printf("\n创建租约后:\n")
	fmt.Printf("  OwingBalance: $%.2f\n", lease.OwingBalance)
	fmt.Printf("  预期值: $%.2f\n", lease.RentAmount+lease.AdditionalMonthlyFees)

	// 验证结果
	expectedBalance := lease.RentAmount + lease.AdditionalMonthlyFees
	if lease.OwingBalance == expectedBalance {
		fmt.Printf("✅ 测试通过: 初始余额设置正确\n")
	} else {
		fmt.Printf("❌ 测试失败: 初始余额不正确\n")
	}

	// 测试不同的租金组合
	testCases := []struct {
		rentAmount      float64
		additionalFees  float64
		expectedBalance float64
	}{
		{1000.0, 0.0, 1000.0},   // 无附加费用
		{800.0, 150.0, 950.0},   // 有附加费用
		{1200.0, 300.0, 1500.0}, // 高租金+高附加费用
		{500.0, 50.0, 550.0},    // 低租金+低附加费用
	}

	fmt.Printf("\n=== 测试不同租金组合 ===\n")
	for i, tc := range testCases {
		testLease := &TestLease{
			RentAmount:            tc.rentAmount,
			AdditionalMonthlyFees: tc.additionalFees,
		}

		// 应用 Scenario 1 逻辑
		testLease.OwingBalance = testLease.RentAmount + testLease.AdditionalMonthlyFees

		fmt.Printf("测试案例 %d:\n", i+1)
		fmt.Printf("  租金: $%.2f + 附加费: $%.2f = 初始余额: $%.2f\n",
			tc.rentAmount, tc.additionalFees, testLease.OwingBalance)

		if testLease.OwingBalance == tc.expectedBalance {
			fmt.Printf("  ✅ 通过\n")
		} else {
			fmt.Printf("  ❌ 失败 (预期: $%.2f)\n", tc.expectedBalance)
		}
	}

	fmt.Printf("\n=== Scenario 1 测试完成 ===\n")
}

func main() {
	TestScenario1()
}
