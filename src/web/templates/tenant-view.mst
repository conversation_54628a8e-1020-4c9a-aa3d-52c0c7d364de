<!-- View/Edit Tenant Modal -->
<div class="fixed inset-0 z-50">
  <!-- Dark overlay with click handler to close -->
  <div
    class="fixed inset-0 bg-black bg-opacity-50 pointer-events-auto"
    onclick="document.getElementById('modal').innerHTML = ''; document.body.style.overflow = ''; document.documentElement.style.overflow = '';"
  ></div>

  <!-- Modal container -->
  <div
    class="fixed inset-0 flex items-center justify-center pointer-events-none"
  >
    <div
      class="bg-white sm:rounded-lg shadow-xl max-w-[1120px] sm:w-9/12 w-full h-full sm:max-h-[80vh] sm:min-h-[70vh] overflow-hidden pointer-events-auto relative"
    >
      <!-- Modal Header -->
      <div
        class="sticky top-0 px-6 py-4 border-b bg-white z-10 flex justify-between items-center"
      >
        <div>
          <h3 class="text-lg font-semibold text-gray-900">View Tenant</h3>
          <p>View or edit tenant</p>
        </div>
        <button
          class="p-2 hover:bg-gray-100 rounded-full"
          onclick="document.getElementById('modal').innerHTML = ''; document.body.style.overflow = ''; document.documentElement.style.overflow = '';"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4 overflow-auto" style="height: calc(100% - 140px)">
        <form>
          <div class="flex flex-wrap">
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="first-name">First Name (Legal)*</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="first-name"
                  placeholder="Tenant First Name"
                  value="{{firstName}}"
                />
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="middle-name">Middle Name</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="middle-name"
                  placeholder=""
                  value="{{middleName}}"
                />
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="last-name">Last Name (Legal)*</label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="last-name"
                  placeholder="Tenant Last Name"
                  value="{{lastName}}"
                />
              </div>
            </div>
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4 mb-8">
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="rent-due">Email*<span id="email-error" class="text-red-500 text-xs ml-2 hidden">Required</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="email"
                  placeholder=""
                  value="{{email}}"
                  required
                />
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="phone-number">Phone Number<span id="phone-number-error" class="text-red-500 text-xs ml-2 hidden">Phone number must be 10 digits</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="phone-number"
                  placeholder=""
                  value="{{phoneNumber}}"
                />
              </div>
              <div class="flex flex-wrap w-full">
                <label class="w-full mb-2" for="SIN">SIN Number<span id="sin-error" class="text-red-500 text-xs ml-2 hidden">SIN number must be 9 digits</span></label>
                <input
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="SIN"
                  placeholder=""
                  value="{{sinNumber}}"
                />
              </div>
            </div>
            <!-- Date of Birth -->
            <div class="w-full flex flex-col sm:flex-row gap-4 mt-4 mb-8">
              <div class="flex flex-wrap sm:w-1/3 w-full">
                <label class="w-full mb-2" for="dateBirth">Date of Birth</label>
                <input
                  type="date"
                  class="p-2 py-4 w-full border border-gray-300 rounded-md focus:outline-none"
                  id="dateBirth"
                  value="{{dateOfBirth}}"
                  min="1900-01-01"
                  max="9999-12-31"
                  required
                />
              </div>
            </div>
            <!-- Tenant Notes-->
            <label class="w-full mb-2" for="tenant-notes" required>
              <h2 class="text-lg font-semibold text-gray-900">Tenant Notes</h2>
              <p class="text-sm text-gray-500">
                A place to keep notes for internal purposes. Visible to only you
                and your team.
              </p>
            </label>
            <textarea
              class="p-4 w-full border border-gray-300 mb-4 rounded-md focus:outline-none h-48 resize-none"
              id="tenant-notes"
              placeholder="Add notes about the tenant here"
            >{{notes}}</textarea>

            <!-- Tenant Actions Section -->
            <div class="mt-8 mb-16">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Tenant Actions</h3>
              <div class="flex flex-col sm:flex-row gap-3">
                <button
                  id="{{#isCurrentTenant}}move-to-past-tenant{{/isCurrentTenant}}{{^isCurrentTenant}}move-to-current-tenant{{/isCurrentTenant}}"
                  data-tenant-id="{{id}}"
                  class="px-4 py-2 text-sm font-medium text-orange-600 bg-orange-50 border border-orange-300 rounded-md hover:bg-orange-100"
                >
                  {{moveButtonText}}
                </button>
                <button
                  id="delete-tenant"
                  data-tenant-id="{{id}}"
                  class="px-4 py-2 text-sm font-medium text-red-500 bg-red-50 border border-red-500 rounded-md hover:bg-red-100"
                >
                  Delete
                </button>
              </div>
              <div class="mt-2 text-sm text-gray-600">
                <p><strong>{{moveButtonText}}:</strong> {{moveButtonDescription}}</p>
                <p><strong>Delete:</strong> Permanently removes tenant from system</p>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div
        class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gray-50 sm:rounded-b-lg flex justify-between gap-2 border-t"
      >
        <button
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          onclick="document.getElementById('modal').innerHTML = ''; document.body.style.overflow = ''; document.documentElement.style.overflow = '';"
        >
          Cancel
        </button>
        <button
          id="update-tenant"
          data-tenant-id="{{id}}"
            class="px-4 py-2 text-sm font-medium text-white bg-slate-800 rounded-md hover:bg-slate-900"
          >
            Update
          </button>
        </div>
      </div>
    </div>
  </div>
</div> 