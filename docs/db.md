## MongoDB Collections Design from OpenAPI Specification

**1. users Collection:**
   - **Collection Name:** `users`
   - **Fields:**
     - `_id`: String (UUID format)
     - `email`: String (maps to `User.email`, unique index)
     - `usrNm`: String (Username, maps to `User.username`, unique index)
     - `pwd`: String (Hashed password using bcrypt)
     - `isVerified`: <PERSON><PERSON><PERSON> (Whether account is verified)
     - `verifyCode`: String (Verification code for email/account verification)
     - `codeExpiry`: Date (Expiration timestamp for verification code)
     - `role`: String (User role: optional, enum["owner", "admin", "member", "dev"])
     - `settings`: Object (maps to `User.settings`, can be flexible schema)
     - `acctTp`: String (Account type)
     - `orgId`: String (Organization ID, optional)
     - `isOAuth`: Boolean (Whether account is OAuth-based, default: false)

**2. properties Collection:**
   - **Collection Name:** `properties`
   - **Fields:**
     - `_id`: ObjectId (maps to `Property.id`)
     - `usrId`: ObjectId (Operating user ID, foreign key referencing `users._id`, identifies who created/manages this property)
     - `orgId`: ObjectId (Organization ID, optional, maps to `Property.organizationId`)
     - `nm`: String (Name, maps to `Property.name`)
     - `addr`: Object (Address, maps to `Property.address`)
       - `street`: String
       - `unit`: String (Unit Number)
       - `city`: String
       - `prov`: String (Province)
       - `county`: String (State/county)
       - `zip`: String (Zip Code)
     - `propTp`: String (Property Type, enum: apartment/house/condo/commercial, maps to `Property.propertyType`)
     - `totRms`: Number (maps to `Property.totalRooms`)
     - `vacRms`: Number (maps to `Property.vacantRooms`)
     - `stat`: String (enum, maps to `Property.status`)
     - `notes`: String (maps to `Property.notes`)
     - `rooms`: Array (Array of room objects)
       - Each room object contains:
         - `_id`: ObjectId
         - `nm`: String (Name, maps to `Room.name`)
         - `tp`: String (Type, enum, maps to `Room.type`)
         - `stat`: String (enum, maps to `Room.status`)
         - `notes`: String (maps to `Room.notes`)

**3. leases Collection:**
   - **Collection Name:** `leases`
   - **Fields:**
     - `_id`: ObjectId (maps to `Lease.id`)
     - `propId`: ObjectId (Property ID, foreign key referencing `properties._id`, maps to `Lease.propertyId`)
     - `roomId`: ObjectId (Room ID, foreign key referencing `rooms._id`, maps to `Lease.roomId`)
     - `usrId`: ObjectId (Operating user ID, foreign key referencing `users._id`, identifies who created/manages this lease)
     - `orgId`: ObjectId (Organization ID, optional, maps to `Lease.organizationId`)
     - `tenantId`: ObjectId (Tenant ID, foreign key referencing `tenants._id`, maps to `Lease.tenantId`)
     - `rentAm`: Number (Rent Amount, maps to `Lease.rentAmount`)
     - `startDt`: Date (Start Date, maps to `Lease.startDate`)
     - `endDt`: Date (End Date, maps to `Lease.endDate`)
     - `stat`: String (enum: active/ended/terminated, maps to `Lease.status`)
     - `addFees`: Number (Additional Monthly Fees, maps to `Lease.additionalMonthlyFees`)
     - `keyDep`: Number (Key Deposit, maps to `Lease.keyDeposit`)
     - `rentDep`: Number (Rent Deposit, maps to `Lease.rentDeposit`)
     - `otherDep`: Number (Other Deposits, maps to `Lease.otherDeposits`)
     - `rentDueDay`: Number (maps to `Lease.rentDueDay`)
     - `rentRep`: Boolean (Rent Reporting, maps to `Lease.rentReporting`)
     - `autoPay`: Boolean (Auto Pay, maps to `Lease.autoPay`)
     - `owingBal`: Number (Owing Balance, maps to `Lease.owingBalance`)
     - `lastPmtDt`: Date (Last Payment Date, maps to `Lease.lastPaymentDate`)

**4. tenant_payments Collection:**
   - **Collection Name:** `tenant_payments`
   - **Fields:**
     - `_id`: ObjectId (maps to `TenantPayment.id`)
     - `leaseId`: ObjectId (Lease ID, foreign key referencing `leases._id`, maps to `TenantPayment.leaseId`)
     - `usrId`: ObjectId (Operating user ID, foreign key referencing `users._id`, identifies who created/manages this payment)
     - `orgId`: ObjectId (Organization ID, optional, maps to `TenantPayment.organizationId`)
     - `amt`: Number (Amount, maps to `TenantPayment.amount`)
     - `dt`: Date (Date, maps to `TenantPayment.date`)
     - `notes`: String (maps to `TenantPayment.notes`)
     - `remBal`: Number (Remaining Balance, maps to `TenantPayment.remainingBalance`)
     - `stat`: String (enum: pending/completed/failed, maps to `TenantPayment.status`)

**5. tenants Collection:**
   - **Collection Name:** `tenants`
   - **Fields:**
     - `_id`: ObjectId (maps to `Tenant.id`)
     - `leaseId`: ObjectId (Lease ID, foreign key referencing `leases._id`, maps to `Tenant.leaseId`)
     - `usrId`: ObjectId (Operating user ID, foreign key referencing `users._id`, identifies who created/manages this tenant)
     - `orgId`: ObjectId (Organization ID, optional, maps to `Tenant.organizationId`)
     - `firstNm`: String (First Name, maps to `Tenant.firstName`) 
     - `midNm`: String (Middle Name, maps to `Tenant.middleName`)
     - `lastNm`: String (Last Name, maps to `Tenant.lastName`)
     - `email`: String (maps to `Tenant.email`)
     - `phone`: String (Phone Number, maps to `Tenant.phoneNumber`) [ENCRYPTED]
     - `sin`: String (SIN Number, maps to `Tenant.sinNumber`) [ENCRYPTED]
     - `dob`: Date (Date of Birth, maps to `Tenant.dateOfBirth`) [ENCRYPTED]
     - `notes`: String (maps to `Tenant.notes`)

**Notes for Encryption:**
- All PII (Personal Identifiable Information) must be encrypted at rest using strong encryption (AES-256 or equivalent)
- Encryption keys should be managed separately using a secure key management system
- Fields marked with [ENCRYPTED] must be encrypted before storage and decrypted for use
- Search operations on encrypted fields require special handling:
  - Consider using deterministic encryption for fields that need to be searchable
  - Use secure indexing techniques for encrypted fields that need to be queried
  - Implement proper access controls for decryption operations
- Encryption should happen at the application layer before data reaches the database
- Backup procedures must maintain encryption of sensitive data
- Encryption keys rotation procedures should be established
- Compliance with PIPEDA (Canadian privacy law) must be maintained

**6. metro2_reports Collection:**
   - **Collection Name:** `metro2_reports`
   - **Fields:**
     - `_id`: ObjectId (maps to `Metro2Report.id`)
     - `usrId`: ObjectId (Operating user ID, foreign key referencing `users._id`)
     - `orgId`: ObjectId (Organization ID, optional)
     - `metro2Data`: Object(the input json data that is used for generating Metro2 file)

**7. problems Collection:**
   - **Collection Name:** `problems`
   - **Fields:**
     - `_id`: ObjectId (maps to `Problem.id`)
     - `usrId`: ObjectId (Operating user ID, foreign key referencing `users._id`)
     - `orgId`: ObjectId (Organization ID, optional)
     - `type`: String (Problem type, enum: billing/payment/system/other)
     - `desc`: String (Problem description)
     - `attachment`: String (Path to attached file, optional)
     - `contactEmail`: String (Contact email)
     - `stat`: String (Status, enum: pending/in_progress/resolved/closed)
     - `leaseId`: ObjectId (Lease ID, optional, foreign key referencing `leases._id`)
     - `propertyId`: ObjectId (Property ID, optional, foreign key referencing `properties._id`)
     - `createdAt`: Date (Creation timestamp)
     - `updatedAt`: Date (Last update timestamp)

**Notes:**

-   `PropertyList`, `