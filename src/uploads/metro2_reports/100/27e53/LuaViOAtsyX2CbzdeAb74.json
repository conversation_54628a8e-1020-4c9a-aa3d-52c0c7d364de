{"data": [{"base": {"accountStatus": "11", "accountType": "29", "actualPaymentAmount": 1000, "addressIndicator": "Y", "city": "Toronto", "consumerAccountNumber": "TNgYhEM4rmuDP", "countryCode": "CA", "currentBalance": 0, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1984-11-10T00:00:00Z", "dateLastPayment": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "1", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 1000, "identificationNumber": "467RE01193", "middleName": "<PERSON>", "paymentHistoryProfile": "00BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 1000, "secondLineAddress": "Test Unit", "socialSecurityNumber": *********, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}}, {"base": {"accountStatus": "11", "accountType": "29", "actualPaymentAmount": 2000, "addressIndicator": "Y", "city": "Toronto", "consumerAccountNumber": "TN7hgpM87VjpZ", "countryCode": "CA", "currentBalance": 2000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1985-07-19T00:00:00Z", "dateLastPayment": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "1", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 2000, "identificationNumber": "467RE01193", "middleName": "<PERSON>", "paymentHistoryProfile": "00BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 2000, "secondLineAddress": "Test Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}}, {"base": {"accountStatus": "78", "accountType": "29", "actualPaymentAmount": 0, "addressIndicator": "Y", "amountPastDue": 9000, "city": "Toronto", "consumerAccountNumber": "TNB258pS4ZOlS", "countryCode": "CA", "currentBalance": 9000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1973-11-10T00:00:00Z", "dateFirstDelinquency": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "1", "firstLineAddress": "Test Avenue", "firstName": "Sophia", "highestCredit": 3000, "identificationNumber": "467RE01193", "middleName": "", "paymentHistoryProfile": "11BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 3000, "secondLineAddress": "Test Unit", "socialSecurityNumber": *********, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}}, {"base": {"accountStatus": "11", "accountType": "29", "actualPaymentAmount": 4000, "addressIndicator": "Y", "city": "Toronto", "consumerAccountNumber": "TN7W6o9bCORoe", "countryCode": "CA", "currentBalance": 0, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "2000-03-09T00:00:00Z", "dateLastPayment": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 4000, "identificationNumber": "467RE01193", "middleName": "", "paymentHistoryProfile": "00BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 4000, "secondLineAddress": "Test Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "1991-11-16T00:00:00Z", "ecoaCode": "2", "firstName": "Ava", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}]}, {"base": {"accountStatus": "11", "accountType": "29", "actualPaymentAmount": 5000, "addressIndicator": "Y", "city": "Toronto", "consumerAccountNumber": "TNzBONSHzGrq2", "countryCode": "CA", "currentBalance": 5000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1989-12-09T00:00:00Z", "dateLastPayment": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 5000, "identificationNumber": "467RE01193", "middleName": "<PERSON>", "paymentHistoryProfile": "00BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 5000, "secondLineAddress": "Test Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "1998-11-09T00:00:00Z", "ecoaCode": "2", "firstName": "Isabella", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}]}, {"base": {"accountStatus": "78", "accountType": "29", "actualPaymentAmount": 0, "addressIndicator": "Y", "amountPastDue": 18000, "city": "Toronto", "consumerAccountNumber": "TNTab1RcaTQcL", "countryCode": "CA", "currentBalance": 18000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1997-12-09T00:00:00Z", "dateFirstDelinquency": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 6000, "identificationNumber": "467RE01193", "middleName": "<PERSON>", "paymentHistoryProfile": "11BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 6000, "secondLineAddress": "Test Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "<PERSON><PERSON><PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "1994-08-20T00:00:00Z", "ecoaCode": "2", "firstName": "<PERSON>", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}]}, {"base": {"accountStatus": "11", "accountType": "29", "actualPaymentAmount": 7000, "addressIndicator": "Y", "city": "Toronto", "consumerAccountNumber": "TNTrFCn0aUOf2", "countryCode": "CA", "currentBalance": 0, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1993-07-09T00:00:00Z", "dateLastPayment": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 7000, "identificationNumber": "467RE01193", "middleName": "<PERSON>", "paymentHistoryProfile": "00BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 7000, "secondLineAddress": "Test Unit", "socialSecurityNumber": *********, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "2000-08-09T00:00:00Z", "ecoaCode": "2", "firstName": "Charlotte", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}, {"dateBirth": "1994-12-24T00:00:00Z", "ecoaCode": "2", "firstName": "<PERSON>", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}]}, {"base": {"accountStatus": "11", "accountType": "29", "actualPaymentAmount": 8000, "addressIndicator": "Y", "city": "Toronto", "consumerAccountNumber": "TNdAzboElHPZ5", "countryCode": "CA", "currentBalance": 8000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "2002-07-12T00:00:00Z", "dateLastPayment": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 8000, "identificationNumber": "467RE01193", "middleName": "", "paymentHistoryProfile": "00BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 8000, "secondLineAddress": "Test Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "2000-09-08T00:00:00Z", "ecoaCode": "2", "firstName": "<PERSON>", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}, {"dateBirth": "2006-07-09T00:00:00Z", "ecoaCode": "2", "firstName": "Amelia", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}]}, {"base": {"accountStatus": "78", "accountType": "29", "actualPaymentAmount": 0, "addressIndicator": "Y", "amountPastDue": 27000, "city": "Toronto", "consumerAccountNumber": "TNTOFR3pmKRAi", "countryCode": "CA", "currentBalance": 27000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1998-07-17T00:00:00Z", "dateFirstDelinquency": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "Test Avenue", "firstName": "<PERSON>", "highestCredit": 9000, "identificationNumber": "467RE01193", "middleName": "", "paymentHistoryProfile": "11BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 9000, "secondLineAddress": "Test Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "<PERSON>", "telephoneNumber": **********, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "2003-11-15T00:00:00Z", "ecoaCode": "2", "firstName": "<PERSON>", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}, {"dateBirth": "1989-09-07T00:00:00Z", "ecoaCode": "2", "firstName": "<PERSON>", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "<PERSON>", "telephoneNumber": **********}]}, {"base": {"accountStatus": "78", "accountType": "29", "actualPaymentAmount": 0, "addressIndicator": "Y", "amountPastDue": 2000, "city": "Toronto", "consumerAccountNumber": "TNSVWNb76w1YE", "countryCode": "CA", "currentBalance": 2000, "dateAccountInformation": "2025-06-30T00:00:00Z", "dateBirth": "1995-07-22T00:00:00Z", "dateFirstDelinquency": "2025-06-01T00:00:00Z", "dateOpened": "2025-05-01T00:00:00Z", "ecoaCode": "2", "firstLineAddress": "15 Holmes Avenue", "firstName": "xia<PERSON>u", "highestCredit": 1000, "identificationNumber": "467RE01193", "middleName": "", "paymentHistoryProfile": "11BBBBBBBBBBBBBBBBBBBBBB", "portfolioType": "O", "processingIndicator": 1, "recordDescriptorWord": 626, "scheduledMonthlyPaymentAmount": 1000, "secondLineAddress": "503 Unit", "socialSecurityNumber": 0, "specialComment": "", "state": "ON", "surname": "li", "telephoneNumber": 0, "termsDuration": "001", "timeStamp": "2025-07-31T14:10:30Z", "zipCode": "M2N 4L8"}, "j1": [{"dateBirth": "1992-07-11T00:00:00Z", "ecoaCode": "2", "firstName": "xia<PERSON>u", "generationCode": "", "segmentIdentifier": "J1", "socialSecurityNumber": 0, "surname": "li", "telephoneNumber": 0}]}], "header": {"EquifaxProgramIdentifier": "1101653", "activityDate": "2025-06-30T00:00:00Z", "dateCreated": "2025-07-31T00:00:00Z", "programDate": "2025-07-31T00:00:00Z", "programRevisionDate": "2025-07-31T00:00:00Z", "recordDescriptorWord": 626, "recordIdentifier": "HEADER", "reporterAddress": "50 Acadia Ave #130, Markham, Ontario, Canada L3R 5Z2", "reporterName": "REALMASTER", "reporterTelephoneNumber": 6475185728}}