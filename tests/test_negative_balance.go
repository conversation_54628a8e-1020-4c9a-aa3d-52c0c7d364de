package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID           string
	OwingBalance float64
	RentAmount   float64
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
}

// 模拟支付后余额更新（新逻辑：允许负数）
func updateBalanceAfterPayment(lease *TestLease, paymentAmount float64) {
	// 计算新余额：当前余额 - 支付金额
	// 允许负数余额，表示多付款
	lease.OwingBalance -= paymentAmount
}

// 模拟租金到期日余额更新（新逻辑：允许负数）
func updateBalanceOnDueDay(lease *TestLease, monthlyRent float64) {
	// 计算新余额：当前余额 + 月租金
	// 允许负数余额，表示多付款
	lease.OwingBalance += monthlyRent
}

// 模拟 Metro2 报告余额计算（负数转为0）
func calculateMetro2Balance(owingBalance float64) float64 {
	if owingBalance < 0 {
		return 0.0
	}
	return owingBalance
}

// TestNegativeBalance 测试负数余额功能
func TestNegativeBalance() {
	fmt.Println("=== 测试负数余额功能 ===")
	fmt.Println("新规则: owingBalance 可以为负数（表示多付款）")
	fmt.Println("Metro2 报告: 负数余额显示为 0")
	fmt.Println()

	// 测试案例
	testCases := []struct {
		name            string
		initialBalance  float64
		paymentAmount   float64
		expectedBalance float64
		expectedMetro2  float64
		description     string
	}{
		{
			name:            "正常支付",
			initialBalance:  1000.0,
			paymentAmount:   300.0,
			expectedBalance: 700.0,
			expectedMetro2:  700.0,
			description:     "支付后余额仍为正数",
		},
		{
			name:            "完全支付",
			initialBalance:  500.0,
			paymentAmount:   500.0,
			expectedBalance: 0.0,
			expectedMetro2:  0.0,
			description:     "支付后余额为零",
		},
		{
			name:            "超额支付",
			initialBalance:  800.0,
			paymentAmount:   1000.0,
			expectedBalance: -200.0,
			expectedMetro2:  0.0,
			description:     "支付超过余额，产生负数余额（多付款）",
		},
		{
			name:            "大额超付",
			initialBalance:  300.0,
			paymentAmount:   2000.0,
			expectedBalance: -1700.0,
			expectedMetro2:  0.0,
			description:     "大额超额支付",
		},
		{
			name:            "零余额支付",
			initialBalance:  0.0,
			paymentAmount:   100.0,
			expectedBalance: -100.0,
			expectedMetro2:  0.0,
			description:     "零余额时的支付",
		},
	}

	fmt.Printf("=== 支付场景测试 ===\n")
	for i, tc := range testCases {
		fmt.Printf("测试 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)

		// 创建测试租约
		lease := TestLease{
			ID:           fmt.Sprintf("lease-%d", i+1),
			OwingBalance: tc.initialBalance,
			RentAmount:   1000.0,
		}

		fmt.Printf("  初始余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  支付金额: $%.2f\n", tc.paymentAmount)

		// 应用支付
		updateBalanceAfterPayment(&lease, tc.paymentAmount)

		fmt.Printf("  支付后余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  Metro2 显示: $%.2f\n", calculateMetro2Balance(lease.OwingBalance))

		// 验证结果
		if lease.OwingBalance == tc.expectedBalance {
			fmt.Printf("  ✅ 余额计算正确\n")
		} else {
			fmt.Printf("  ❌ 余额计算错误 (期望: $%.2f, 实际: $%.2f)\n",
				tc.expectedBalance, lease.OwingBalance)
		}

		metro2Balance := calculateMetro2Balance(lease.OwingBalance)
		if metro2Balance == tc.expectedMetro2 {
			fmt.Printf("  ✅ Metro2 显示正确\n")
		} else {
			fmt.Printf("  ❌ Metro2 显示错误 (期望: $%.2f, 实际: $%.2f)\n",
				tc.expectedMetro2, metro2Balance)
		}

		fmt.Println()
	}

	// 测试到期日余额更新
	fmt.Printf("=== 到期日余额更新测试 ===\n")

	dueDayTests := []struct {
		name            string
		initialBalance  float64
		monthlyRent     float64
		expectedBalance float64
		description     string
	}{
		{
			name:            "正余额到期",
			initialBalance:  500.0,
			monthlyRent:     1000.0,
			expectedBalance: 1500.0,
			description:     "正余额基础上增加月租",
		},
		{
			name:            "零余额到期",
			initialBalance:  0.0,
			monthlyRent:     800.0,
			expectedBalance: 800.0,
			description:     "零余额基础上增加月租",
		},
		{
			name:            "负余额到期",
			initialBalance:  -200.0,
			monthlyRent:     1000.0,
			expectedBalance: 800.0,
			description:     "负余额（多付款）基础上增加月租",
		},
		{
			name:            "大额负余额到期",
			initialBalance:  -1500.0,
			monthlyRent:     1000.0,
			expectedBalance: -500.0,
			description:     "大额负余额基础上增加月租，仍为负数",
		},
	}

	for i, tc := range dueDayTests {
		fmt.Printf("测试 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)

		// 创建测试租约
		lease := TestLease{
			ID:           fmt.Sprintf("due-lease-%d", i+1),
			OwingBalance: tc.initialBalance,
			RentAmount:   tc.monthlyRent,
		}

		fmt.Printf("  到期前余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  月租金: $%.2f\n", tc.monthlyRent)

		// 应用到期日更新
		updateBalanceOnDueDay(&lease, tc.monthlyRent)

		fmt.Printf("  到期后余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  Metro2 显示: $%.2f\n", calculateMetro2Balance(lease.OwingBalance))

		// 验证结果
		if lease.OwingBalance == tc.expectedBalance {
			fmt.Printf("  ✅ 到期日余额更新正确\n")
		} else {
			fmt.Printf("  ❌ 到期日余额更新错误 (期望: $%.2f, 实际: $%.2f)\n",
				tc.expectedBalance, lease.OwingBalance)
		}

		fmt.Println()
	}

	// 综合场景测试
	fmt.Printf("=== 综合场景测试 ===\n")

	// 场景：租客预付了几个月的租金
	fmt.Printf("场景: 租客预付3个月租金\n")
	comprehensiveLease := TestLease{
		ID:           "comprehensive-lease",
		OwingBalance: 1000.0, // 当前欠款
		RentAmount:   1000.0, // 月租金
	}

	fmt.Printf("  初始状态: 欠款 $%.2f\n", comprehensiveLease.OwingBalance)

	// 租客支付了4个月的租金（当前欠款 + 预付3个月）
	paymentAmount := 4000.0
	fmt.Printf("  租客支付: $%.2f (4个月租金)\n", paymentAmount)

	updateBalanceAfterPayment(&comprehensiveLease, paymentAmount)
	fmt.Printf("  支付后余额: $%.2f (预付了3个月)\n", comprehensiveLease.OwingBalance)
	fmt.Printf("  Metro2 显示: $%.2f\n", calculateMetro2Balance(comprehensiveLease.OwingBalance))

	// 模拟3个月后的到期日更新
	fmt.Printf("\n  3个月后的到期日更新:\n")
	for month := 1; month <= 3; month++ {
		fmt.Printf("    第%d个月到期:\n", month)
		fmt.Printf("      到期前余额: $%.2f\n", comprehensiveLease.OwingBalance)

		updateBalanceOnDueDay(&comprehensiveLease, comprehensiveLease.RentAmount)

		fmt.Printf("      到期后余额: $%.2f\n", comprehensiveLease.OwingBalance)
		fmt.Printf("      Metro2 显示: $%.2f\n", calculateMetro2Balance(comprehensiveLease.OwingBalance))
	}

	if comprehensiveLease.OwingBalance == 0.0 {
		fmt.Printf("  ✅ 3个月后余额恢复正常（预付款用完，无欠款）\n")
	} else {
		fmt.Printf("  ❌ 3个月后余额异常 (实际: $%.2f, 期望: $0.00)\n", comprehensiveLease.OwingBalance)
	}

	// 总结
	fmt.Printf("\n=== 功能总结 ===\n")
	fmt.Printf("✅ owingBalance 可以为负数（表示多付款/预付款）\n")
	fmt.Printf("✅ 支付操作允许产生负数余额\n")
	fmt.Printf("✅ 到期日更新在负数余额基础上正确计算\n")
	fmt.Printf("✅ Metro2 报告将负数余额显示为 0\n")
	fmt.Printf("✅ 支持预付款场景的完整生命周期\n")

	fmt.Printf("\n=== 负数余额测试完成 ===\n")
}

func main() {
	TestNegativeBalance()
}
