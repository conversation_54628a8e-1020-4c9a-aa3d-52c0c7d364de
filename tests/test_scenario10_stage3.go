package main

import (
	"fmt"
	"time"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID                  string
	Status              string
	AutoPay             bool
	RentDueDay          int
	RentAmount          float64
	AdditionalMonthlyFees float64
	OwingBalance        float64
	LastPaymentDate     string
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
	Date    string
}

// 模拟支付记录存储
var paymentRecords []TestPayment

// 模拟 checkPaymentExists 函数
func checkPaymentExists(leaseID string, date time.Time) (bool, error) {
	targetMonth := date.Format("2006-01")
	
	for _, payment := range paymentRecords {
		if payment.LeaseID == leaseID {
			paymentDate, err := time.Parse("2006-01-02", payment.Date)
			if err == nil && paymentDate.Format("2006-01") == targetMonth {
				return true, nil
			}
		}
	}
	
	return false, nil
}

// 模拟 checkLastProcessDate 函数
func checkLastProcessDate(lease TestLease, date time.Time) (bool, error) {
	if lease.LastPaymentDate != "" {
		lastProcessDate, err := time.Parse("2006-01-02", lease.LastPaymentDate)
		if err == nil {
			// 如果最后处理日期是今天，说明已经处理过了
			if lastProcessDate.Format("2006-01-02") == date.Format("2006-01-02") {
				return true, nil
			}
		}
	}
	
	return false, nil
}

// 模拟 checkBalanceUpdateExists 函数
func checkBalanceUpdateExists(lease TestLease, date time.Time) (bool, error) {
	// 检查当月是否已有支付记录
	paymentExists, err := checkPaymentExists(lease.ID, date)
	if err != nil {
		return false, err
	}
	
	if paymentExists {
		return true, nil // 有支付记录，说明已经处理过了
	}
	
	// 检查是否已经更新过余额（通过 lastPmtDt 字段）
	balanceUpdateExists, err := checkLastProcessDate(lease, date)
	if err != nil {
		return false, err
	}
	
	return balanceUpdateExists, nil
}

// 模拟处理函数（带防重复机制）
func processLeaseWithDuplicateCheck(lease *TestLease, today time.Time) (string, error) {
	// 检查是否已经处理过
	exists, err := checkBalanceUpdateExists(*lease, today)
	if err != nil {
		return "", err
	}
	
	if exists {
		return "already_processed", nil
	}
	
	if lease.AutoPay {
		// 创建支付记录
		payment := TestPayment{
			ID:      fmt.Sprintf("payment-%s-%s", lease.ID, today.Format("2006-01")),
			LeaseID: lease.ID,
			Amount:  lease.RentAmount + lease.AdditionalMonthlyFees,
			Date:    today.Format("2006-01-02"),
		}
		
		// 添加到支付记录
		paymentRecords = append(paymentRecords, payment)
		
		// 更新余额
		lease.OwingBalance -= payment.Amount
		if lease.OwingBalance < 0 {
			lease.OwingBalance = 0
		}
		
		// 更新最后处理日期
		lease.LastPaymentDate = today.Format("2006-01-02")
		
		return "auto_payment_created", nil
	} else {
		// 只更新余额
		monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees
		lease.OwingBalance += monthlyRent
		
		// 更新最后处理日期
		lease.LastPaymentDate = today.Format("2006-01-02")
		
		return "balance_updated", nil
	}
}

// TestScenario10Stage3 测试阶段3：防重复机制
func TestScenario10Stage3() {
	fmt.Println("=== 测试 Scenario 10 阶段3：防重复机制 ===")
	
	today := time.Now()
	fmt.Printf("当前日期: %s\n\n", today.Format("2006-01-02"))

	// 清空支付记录
	paymentRecords = []TestPayment{}

	// 创建测试租约
	testLease := TestLease{
		ID:                    "lease-duplicate-test",
		Status:                "active",
		AutoPay:               false,
		RentDueDay:            today.Day(),
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          1200.0,
		LastPaymentDate:       "",
	}

	fmt.Printf("=== 测试防重复处理 ===\n")
	fmt.Printf("测试租约: %s\n", testLease.ID)
	fmt.Printf("AutoPay: %t\n", testLease.AutoPay)
	fmt.Printf("初始余额: $%.2f\n", testLease.OwingBalance)
	fmt.Printf("月租金: $%.2f\n", testLease.RentAmount+testLease.AdditionalMonthlyFees)

	// 第一次处理
	fmt.Printf("\n1. 第一次处理:\n")
	result1, err1 := processLeaseWithDuplicateCheck(&testLease, today)
	if err1 != nil {
		fmt.Printf("  ❌ 处理失败: %v\n", err1)
	} else {
		fmt.Printf("  ✅ 处理结果: %s\n", result1)
		fmt.Printf("  处理后余额: $%.2f\n", testLease.OwingBalance)
		fmt.Printf("  最后处理日期: %s\n", testLease.LastPaymentDate)
		
		if result1 == "balance_updated" {
			fmt.Printf("  ✅ 第一次处理成功\n")
		} else {
			fmt.Printf("  ❌ 第一次处理结果异常\n")
		}
	}

	// 第二次处理（应该被阻止）
	fmt.Printf("\n2. 第二次处理（重复）:\n")
	originalBalance := testLease.OwingBalance
	result2, err2 := processLeaseWithDuplicateCheck(&testLease, today)
	if err2 != nil {
		fmt.Printf("  ❌ 处理失败: %v\n", err2)
	} else {
		fmt.Printf("  ✅ 处理结果: %s\n", result2)
		fmt.Printf("  处理后余额: $%.2f\n", testLease.OwingBalance)
		
		if result2 == "already_processed" && testLease.OwingBalance == originalBalance {
			fmt.Printf("  ✅ 重复处理被正确阻止\n")
		} else {
			fmt.Printf("  ❌ 重复处理未被阻止\n")
		}
	}

	// 测试 AutoPay=true 的情况
	fmt.Printf("\n=== 测试 AutoPay=true 的防重复 ===\n")
	
	autoPayLease := TestLease{
		ID:                    "lease-autopay-duplicate",
		Status:                "active",
		AutoPay:               true,
		RentDueDay:            today.Day(),
		RentAmount:            800.0,
		AdditionalMonthlyFees: 150.0,
		OwingBalance:          950.0,
		LastPaymentDate:       "",
	}

	fmt.Printf("测试租约: %s (AutoPay=true)\n", autoPayLease.ID)
	fmt.Printf("初始余额: $%.2f\n", autoPayLease.OwingBalance)

	// 第一次处理
	fmt.Printf("\n1. 第一次处理:\n")
	result3, err3 := processLeaseWithDuplicateCheck(&autoPayLease, today)
	if err3 != nil {
		fmt.Printf("  ❌ 处理失败: %v\n", err3)
	} else {
		fmt.Printf("  ✅ 处理结果: %s\n", result3)
		fmt.Printf("  处理后余额: $%.2f\n", autoPayLease.OwingBalance)
		fmt.Printf("  支付记录数: %d\n", len(paymentRecords))
		
		if result3 == "auto_payment_created" {
			fmt.Printf("  ✅ 自动支付创建成功\n")
		}
	}

	// 第二次处理（应该被阻止）
	fmt.Printf("\n2. 第二次处理（重复）:\n")
	originalBalance2 := autoPayLease.OwingBalance
	originalPaymentCount := len(paymentRecords)
	result4, err4 := processLeaseWithDuplicateCheck(&autoPayLease, today)
	if err4 != nil {
		fmt.Printf("  ❌ 处理失败: %v\n", err4)
	} else {
		fmt.Printf("  ✅ 处理结果: %s\n", result4)
		fmt.Printf("  处理后余额: $%.2f\n", autoPayLease.OwingBalance)
		fmt.Printf("  支付记录数: %d\n", len(paymentRecords))
		
		if result4 == "already_processed" && 
		   autoPayLease.OwingBalance == originalBalance2 && 
		   len(paymentRecords) == originalPaymentCount {
			fmt.Printf("  ✅ 重复处理被正确阻止\n")
		} else {
			fmt.Printf("  ❌ 重复处理未被阻止\n")
		}
	}

	// 测试跨日期的处理
	fmt.Printf("\n=== 测试跨日期处理 ===\n")
	
	crossDateLease := TestLease{
		ID:                    "lease-cross-date",
		Status:                "active",
		AutoPay:               false,
		RentDueDay:            today.Day(),
		RentAmount:            600.0,
		AdditionalMonthlyFees: 100.0,
		OwingBalance:          700.0,
		LastPaymentDate:       today.AddDate(0, 0, -1).Format("2006-01-02"), // 昨天处理过
	}

	fmt.Printf("测试租约: %s\n", crossDateLease.ID)
	fmt.Printf("最后处理日期: %s (昨天)\n", crossDateLease.LastPaymentDate)
	fmt.Printf("今天日期: %s\n", today.Format("2006-01-02"))

	result5, err5 := processLeaseWithDuplicateCheck(&crossDateLease, today)
	if err5 != nil {
		fmt.Printf("❌ 处理失败: %v\n", err5)
	} else {
		fmt.Printf("✅ 处理结果: %s\n", result5)
		if result5 == "balance_updated" {
			fmt.Printf("✅ 跨日期处理正确（昨天处理过，今天可以再次处理）\n")
		} else if result5 == "already_processed" {
			fmt.Printf("❌ 跨日期处理错误（昨天处理过不应该影响今天）\n")
		}
	}

	// 统计信息
	fmt.Printf("\n=== 防重复机制总结 ===\n")
	fmt.Printf("检查机制:\n")
	fmt.Printf("  1. 检查当月是否有支付记录\n")
	fmt.Printf("  2. 检查 lastPmtDt 是否为今天\n")
	fmt.Printf("  3. 任一条件满足则跳过处理\n")
	fmt.Printf("\n处理结果:\n")
	fmt.Printf("  - 首次处理: 正常执行\n")
	fmt.Printf("  - 重复处理: 返回 'already_processed'\n")
	fmt.Printf("  - 跨日期: 允许处理\n")
	fmt.Printf("\n总支付记录数: %d\n", len(paymentRecords))

	fmt.Printf("\n=== 阶段3测试完成 ===\n")
}

func main() {
	TestScenario10Stage3()
}
