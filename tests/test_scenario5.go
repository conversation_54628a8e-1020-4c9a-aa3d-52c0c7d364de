package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID                    string
	RentAmount            float64
	AdditionalMonthlyFees float64
	StartDate             string
	EndDate               string
	RentDueDay            int
	PropertyID            string
	RoomID                string
	AutoPay               bool
	RentReporting         bool
	Status                string
}

// 简化的租客结构体用于测试
type TestTenant struct {
	ID      string
	LeaseID string
	Name    string
	Email   string
}

// 模拟 isOnlyStatusChanged 函数
func isOnlyStatusChanged(current, new *TestLease) bool {
	return current.RentAmount == new.RentAmount &&
		current.AdditionalMonthlyFees == new.AdditionalMonthlyFees &&
		current.StartDate == new.StartDate &&
		current.EndDate == new.EndDate &&
		current.RentDueDay == new.RentDueDay &&
		current.PropertyID == new.PropertyID &&
		current.RoomID == new.RoomID &&
		current.AutoPay == new.AutoPay &&
		current.RentReporting == new.RentReporting
}

// 模拟 Scenario 5 的租约修改验证
func simulateLeaseUpdate(currentLease, newLease *TestLease) error {
	// Scenario 5: 如果当前状态不是 active，只允许修改 status 字段
	if currentLease.Status != "active" {
		if !isOnlyStatusChanged(currentLease, newLease) {
			return fmt.Errorf("non-active lease (status: %s) can only modify status field", currentLease.Status)
		}
	}
	return nil
}

// 模拟租客修改验证
func simulateTenantUpdate(lease *TestLease, tenant *TestTenant) error {
	if lease.Status != "active" {
		return fmt.Errorf("cannot modify tenants for non-active lease (status: %s)", lease.Status)
	}
	return nil
}

// 模拟文件上传验证
func simulateFileUpload(lease *TestLease) error {
	if lease.Status != "active" {
		return fmt.Errorf("cannot upload files to non-active lease (status: %s)", lease.Status)
	}
	return nil
}

// TestScenario5 测试租约状态锁定机制
func TestScenario5() {
	fmt.Println("=== 测试 Scenario 5: 租约状态锁定机制 ===")

	// 创建基础租约数据
	baseLease := &TestLease{
		ID:                    "lease-1",
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		StartDate:             "2025-01-01",
		EndDate:               "",
		RentDueDay:            5,
		PropertyID:            "property-1",
		RoomID:                "room-1",
		AutoPay:               true,
		RentReporting:         false,
		Status:                "active",
	}

	// 测试不同状态的租约修改
	statuses := []string{"active", "pending", "ended", "deleted"}

	for _, status := range statuses {
		fmt.Printf("\n=== 测试状态: %s ===\n", status)

		currentLease := *baseLease
		currentLease.Status = status

		// 测试1: 只修改状态（应该总是允许）
		fmt.Printf("1. 只修改状态 (%s → active):\n", status)
		statusOnlyLease := currentLease
		statusOnlyLease.Status = "active"

		err := simulateLeaseUpdate(&currentLease, &statusOnlyLease)
		if err != nil {
			fmt.Printf("  ❌ 失败: %v\n", err)
		} else {
			fmt.Printf("  ✅ 允许修改状态\n")
		}

		// 测试2: 修改租金（只有active状态应该允许）
		fmt.Printf("2. 修改租金 ($%.2f → $%.2f):\n", currentLease.RentAmount, 1200.0)
		rentChangeLease := currentLease
		rentChangeLease.RentAmount = 1200.0

		err = simulateLeaseUpdate(&currentLease, &rentChangeLease)
		if status == "active" {
			if err != nil {
				fmt.Printf("  ❌ active状态应该允许修改租金: %v\n", err)
			} else {
				fmt.Printf("  ✅ active状态允许修改租金\n")
			}
		} else {
			if err != nil {
				fmt.Printf("  ✅ 正确拒绝非active状态的租金修改\n")
			} else {
				fmt.Printf("  ❌ 应该拒绝非active状态的租金修改\n")
			}
		}

		// 测试3: 修改日期（只有active状态应该允许）
		fmt.Printf("3. 修改开始日期 (%s → %s):\n", currentLease.StartDate, "2025-02-01")
		dateChangeLease := currentLease
		dateChangeLease.StartDate = "2025-02-01"

		err = simulateLeaseUpdate(&currentLease, &dateChangeLease)
		if status == "active" {
			if err != nil {
				fmt.Printf("  ❌ active状态应该允许修改日期: %v\n", err)
			} else {
				fmt.Printf("  ✅ active状态允许修改日期\n")
			}
		} else {
			if err != nil {
				fmt.Printf("  ✅ 正确拒绝非active状态的日期修改\n")
			} else {
				fmt.Printf("  ❌ 应该拒绝非active状态的日期修改\n")
			}
		}

		// 测试4: 修改多个字段（只有active状态应该允许）
		fmt.Printf("4. 修改多个字段 (租金+日期+到期日):\n")
		multiChangeLease := currentLease
		multiChangeLease.RentAmount = 1500.0
		multiChangeLease.StartDate = "2025-03-01"
		multiChangeLease.RentDueDay = 10

		err = simulateLeaseUpdate(&currentLease, &multiChangeLease)
		if status == "active" {
			if err != nil {
				fmt.Printf("  ❌ active状态应该允许修改多个字段: %v\n", err)
			} else {
				fmt.Printf("  ✅ active状态允许修改多个字段\n")
			}
		} else {
			if err != nil {
				fmt.Printf("  ✅ 正确拒绝非active状态的多字段修改\n")
			} else {
				fmt.Printf("  ❌ 应该拒绝非active状态的多字段修改\n")
			}
		}

		// 测试5: 租客修改
		fmt.Printf("5. 修改租客信息:\n")
		tenant := &TestTenant{
			ID:      "tenant-1",
			LeaseID: currentLease.ID,
			Name:    "John Doe",
			Email:   "<EMAIL>",
		}

		err = simulateTenantUpdate(&currentLease, tenant)
		if status == "active" {
			if err != nil {
				fmt.Printf("  ❌ active状态应该允许修改租客: %v\n", err)
			} else {
				fmt.Printf("  ✅ active状态允许修改租客\n")
			}
		} else {
			if err != nil {
				fmt.Printf("  ✅ 正确拒绝非active状态的租客修改\n")
			} else {
				fmt.Printf("  ❌ 应该拒绝非active状态的租客修改\n")
			}
		}

		// 测试6: 文件上传
		fmt.Printf("6. 文件上传:\n")
		err = simulateFileUpload(&currentLease)
		if status == "active" {
			if err != nil {
				fmt.Printf("  ❌ active状态应该允许文件上传: %v\n", err)
			} else {
				fmt.Printf("  ✅ active状态允许文件上传\n")
			}
		} else {
			if err != nil {
				fmt.Printf("  ✅ 正确拒绝非active状态的文件上传\n")
			} else {
				fmt.Printf("  ❌ 应该拒绝非active状态的文件上传\n")
			}
		}
	}

	// 边界情况测试
	fmt.Printf("\n=== 边界情况测试 ===\n")

	// 测试同时修改状态和其他字段
	fmt.Printf("1. 同时修改状态和租金 (ended → active + 租金变更):\n")
	endedLease := *baseLease
	endedLease.Status = "ended"

	statusAndRentLease := endedLease
	statusAndRentLease.Status = "active"
	statusAndRentLease.RentAmount = 1300.0

	err := simulateLeaseUpdate(&endedLease, &statusAndRentLease)
	if err != nil {
		fmt.Printf("  ✅ 正确拒绝同时修改状态和其他字段\n")
	} else {
		fmt.Printf("  ❌ 应该拒绝同时修改状态和其他字段\n")
	}

	fmt.Printf("\n=== Scenario 5 测试完成 ===\n")
}

func main() {
	TestScenario5()
}
