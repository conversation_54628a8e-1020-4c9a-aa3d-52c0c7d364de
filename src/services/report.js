const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { Problem } = require('../models');
const mongoose = require('mongoose');
const Grid = require('gridfs-stream');
const { GridFsStorage } = require('multer-gridfs-storage');
require('dotenv').config();

// 使用环境变量中的MongoDB URI
const mongoURI = process.env.MONGODB_URI;
mongoose.connect(mongoURI, { useNewUrlParser: true, useUnifiedTopology: true });

const conn = mongoose.connection;
let gfs;

conn.once('open', () => {
    gfs = Grid(conn.db, mongoose.mongo);
    gfs.collection('uploads');
});

// 配置GridFS存储
const storage = new GridFsStorage({
    url: mongoURI,
    file: (req, file) => {
        return {
            filename: file.originalname,
            bucketName: 'uploads'
        };
    }
});

const upload = multer({ storage });

// 获取用户相关的租约和物业
router.get('/api/user/related-objects', async (req, res) => {
    try {
        const userId = req.user.id;
        // 这里需要根据您的数据模型来实现
        const objects = await Promise.all([
            // 获取用户的租约
            Lease.findAll({ where: { userId } }),
            // 获取用户的物业
            Property.findAll({ where: { userId } })
        ]);

        const formattedObjects = objects.flat().map(obj => ({
            id: obj.id,
            name: obj.name || obj.address,
            type: obj instanceof Lease ? 'lease' : 'property'
        }));

        res.json({
            success: true,
            objects: formattedObjects
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to get related objects'
        });
    }
});

// 提交问题报告
router.post('/api/problems', upload.single('attachment'), async (req, res) => {
    try {
        const {
            problemType,
            description,
            relatedObject
        } = req.body;

        const problem = await Problem.create({
            userId: req.user.id,
            type: problemType,
            description,
            attachment: req.file ? req.file.id : null, // 存储文件ID
            contactEmail: req.user.email,
            status: 'pending'
        });

        // 根据关联对象类型设置相应的ID
        if (relatedObject) {
            const [type, id] = relatedObject.split('-');
            if (type === 'lease') {
                problem.leaseId = id;
            } else if (type === 'property') {
                problem.propertyId = id;
            }
            await problem.save();
        }

        res.json({
            success: true,
            message: 'report has been submitted',
            problemId: problem.id
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Submission failed'
        });
    }
});

// 获取问题报告列表
router.get('/api/problems', async (req, res) => {
    try {
        const problems = await Problem.findAll({
            where: { userId: req.user.id },
            order: [['createdAt', 'DESC']]
        });

        res.json({
            success: true,
            problems
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Get problem list failed'
        });
    }
});

// 获取文件
router.get('/file/:id', (req, res) => {
    gfs.files.findOne({ _id: mongoose.Types.ObjectId(req.params.id) }, (err, file) => {
        if (!file || file.length === 0) {
            return res.status(404).json({ err: 'No file exists' });
        }
        const readstream = gfs.createReadStream({ _id: file._id });
        readstream.pipe(res);
    });
});

module.exports = router; 