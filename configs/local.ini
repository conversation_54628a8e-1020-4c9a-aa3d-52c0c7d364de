[dbs]
verbose = 3

[dbs.rr]
#uri = "mongodb://************:27017,************:27018/rr?replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"
uri = "mongodb://r1:r1@************:27017/rr?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[dbs.rr_test]
#uri = "mongodb://************:27017,************:27018/rr_test?replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"
uri = "mongodb://r1:r1@************:27017/rr_test?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=20&tls=false"

[server]
port = "8089"
#baseUrl = "/web"
baseUrl = "http://localhost:8089"
#static_path = "src/web/dist"
static_path = "web/dist"
# Enable static file serving (recommended for development and single-server deployments)
serve_static = true
# Legacy setting - kept for backward compatibility
developer_mode = true

[golog]
dir = "logs"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[oauthClient]
clientId = "report_rentals"
clientSecret = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib29lzajna"
redirectUrl = "https://d9w.realmaster.com/v1/auth/oauth/callback"
authUrl = "https://d4w.realmaster.com/provider/authorize"
tokenUrl = "https://d4w.realmaster.com/provider/token"
userInfoUrl = "https://d4w.realmaster.com/provider/userinfo"

[encryption]
key = "b9lzajneH2MiTrUeRzaSyCzCBgaRfib2"

[auth]
jwtSecret = "rUeRzaSyCzCBgaRfib2b9lzajneH2MiT"

[stripe]
secret_key = "sk_test_51RYw3ERdRW2qyPyrKO4b5ZdEr5hyioKB7XIhytmyfoYx1yDx8I9b42sSemwBPmiL3pCfEcYXo2NvhECvTGPcpVCR00pCRBwLIF"
webhook_secret = "whsec_3ab1b9d27a7fc917f2b599da1a518d34b563c7351bd828b641a71156375dd116"

[autoPayment]
enabled = true
executionHour = 1
maxRetries = 3

# 租金报告邮件批处理配置
[rentReportEmailBatch]
enabled = true
processIntervalMinutes = 3  # 每3分钟处理一次队列（测试用）
processWindowMinutes = 2    # 处理前2分钟的记录（测试用）
cleanupDays = 7            # 清理7天前的记录