package main

import (
	"fmt"
	"log"

	"github.com/real-rm/goconfig"
)

func main() {
	fmt.Println("🔍 测试配置读取...")

	// 加载配置
	if err := goconfig.LoadConfig(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 测试Metro2自动生成配置读取
	fmt.Println("\n📋 Metro2自动生成配置:")
	
	enabled := goconfig.Config("metro2AutoGeneration.enabled")
	sendDay := goconfig.Config("metro2AutoGeneration.sendDay")
	sendHour := goconfig.Config("metro2AutoGeneration.sendHour")
	recipientEmail := goconfig.Config("metro2AutoGeneration.recipientEmail")
	processInterval := goconfig.Config("metro2AutoGeneration.processIntervalMinutes")
	testMode := goconfig.Config("metro2AutoGeneration.testMode")

	fmt.Printf("  - enabled: %v (type: %T)\n", enabled, enabled)
	fmt.Printf("  - sendDay: %v (type: %T)\n", sendDay, sendDay)
	fmt.Printf("  - sendHour: %v (type: %T)\n", sendHour, sendHour)
	fmt.Printf("  - recipientEmail: %v (type: %T)\n", recipientEmail, recipientEmail)
	fmt.Printf("  - processInterval: %v (type: %T)\n", processInterval, processInterval)
	fmt.Printf("  - testMode: %v (type: %T)\n", testMode, testMode)

	// 测试类型转换
	fmt.Println("\n🔄 类型转换测试:")
	
	if enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			fmt.Printf("  ✅ enabled转换为bool成功: %v\n", enabledBool)
		} else {
			fmt.Printf("  ❌ enabled转换为bool失败\n")
		}
	}

	if testMode != nil {
		if testBool, ok := testMode.(bool); ok {
			fmt.Printf("  ✅ testMode转换为bool成功: %v\n", testBool)
		} else {
			fmt.Printf("  ❌ testMode转换为bool失败\n")
		}
	}

	fmt.Println("\n🎉 配置测试完成！")
}
