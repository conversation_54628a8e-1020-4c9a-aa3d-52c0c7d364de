package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID           string
	OwingBalance float64
	Status       string
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
}

// 模拟 Scenario 4 的完整逻辑
func simulateDeletePayment(lease *TestLease, paymentToDelete *TestPayment) error {
	// Scenario 5: 检查租约状态
	if lease.Status != "active" {
		return fmt.Errorf("can only delete payments from active leases, current status: %s", lease.Status)
	}

	// Scenario 4: 撤回支付对余额的影响
	// 删除支付会增加余额（撤回支付的减少效果）
	newBalance := lease.OwingBalance + paymentToDelete.Amount

	// 余额不能为负（虽然删除支付通常会增加余额）
	if newBalance < 0 {
		newBalance = 0
	}

	lease.OwingBalance = newBalance
	return nil
}

// TestScenario4 测试删除支付时的余额撤回
func TestScenario4() {
	fmt.Println("=== 测试 Scenario 4: Delete Tenant Payment 余额撤回 ===")

	// 测试案例
	testCases := []struct {
		name            string
		initialBalance  float64
		paymentAmount   float64
		expectedBalance float64
		description     string
	}{
		{
			name:            "删除普通支付",
			initialBalance:  500.0,
			paymentAmount:   300.0,
			expectedBalance: 800.0,
			description:     "删除$300支付，余额应该增加$300",
		},
		{
			name:            "删除大额支付",
			initialBalance:  200.0,
			paymentAmount:   1000.0,
			expectedBalance: 1200.0,
			description:     "删除$1000支付，余额应该增加$1000",
		},
		{
			name:            "删除小额支付",
			initialBalance:  1500.0,
			paymentAmount:   50.0,
			expectedBalance: 1550.0,
			description:     "删除$50支付，余额应该增加$50",
		},
		{
			name:            "删除零金额支付",
			initialBalance:  800.0,
			paymentAmount:   0.0,
			expectedBalance: 800.0,
			description:     "删除$0支付，余额应该保持不变",
		},
		{
			name:            "从零余额删除支付",
			initialBalance:  0.0,
			paymentAmount:   400.0,
			expectedBalance: 400.0,
			description:     "从$0余额删除$400支付，余额应该变为$400",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)

		// 创建测试数据
		lease := &TestLease{
			ID:           "lease-1",
			OwingBalance: tc.initialBalance,
			Status:       "active",
		}

		paymentToDelete := &TestPayment{
			ID:      "payment-1",
			LeaseID: "lease-1",
			Amount:  tc.paymentAmount,
		}

		fmt.Printf("  删除前余额: $%.2f\n", lease.OwingBalance)
		fmt.Printf("  要删除的支付: $%.2f\n", paymentToDelete.Amount)

		// 应用 Scenario 4 逻辑
		err := simulateDeletePayment(lease, paymentToDelete)
		if err != nil {
			fmt.Printf("  ❌ 删除失败: %v\n", err)
		} else {
			fmt.Printf("  删除后余额: $%.2f\n", lease.OwingBalance)
			fmt.Printf("  预期余额: $%.2f\n", tc.expectedBalance)

			if lease.OwingBalance == tc.expectedBalance {
				fmt.Printf("  ✅ 通过\n")
			} else {
				fmt.Printf("  ❌ 失败\n")
			}
		}
		fmt.Println()
	}

	// 测试状态锁定
	fmt.Printf("=== 测试状态锁定 ===\n")
	
	statuses := []string{"pending", "ended", "deleted"}
	for _, status := range statuses {
		lease := &TestLease{
			ID:           fmt.Sprintf("lease-%s", status),
			OwingBalance: 1000.0,
			Status:       status,
		}

		payment := &TestPayment{
			ID:      "payment-1",
			LeaseID: lease.ID,
			Amount:  300.0,
		}

		fmt.Printf("租约状态 '%s': ", status)
		err := simulateDeletePayment(lease, payment)
		
		if err != nil {
			fmt.Printf("✅ 正确拒绝\n")
		} else {
			fmt.Printf("❌ 应该拒绝非active状态的删除操作\n")
		}
	}

	// 测试连续删除场景
	fmt.Printf("\n=== 连续删除测试 ===\n")
	continuousLease := &TestLease{
		ID:           "lease-continuous",
		OwingBalance: 100.0,
		Status:       "active",
	}

	payments := []float64{200.0, 300.0, 150.0}
	expectedBalances := []float64{300.0, 600.0, 750.0}

	fmt.Printf("初始余额: $%.2f\n", continuousLease.OwingBalance)

	for i, paymentAmount := range payments {
		payment := &TestPayment{
			ID:      fmt.Sprintf("payment-%d", i+1),
			LeaseID: continuousLease.ID,
			Amount:  paymentAmount,
		}

		fmt.Printf("删除支付 %d: $%.2f\n", i+1, paymentAmount)
		
		err := simulateDeletePayment(continuousLease, payment)
		if err == nil {
			fmt.Printf("  余额变为: $%.2f (预期: $%.2f)\n", 
				continuousLease.OwingBalance, expectedBalances[i])
			
			if continuousLease.OwingBalance == expectedBalances[i] {
				fmt.Printf("  ✅ 通过\n")
			} else {
				fmt.Printf("  ❌ 失败\n")
			}
		} else {
			fmt.Printf("  ❌ 删除失败: %v\n", err)
		}
	}

	// 测试边界情况
	fmt.Printf("\n=== 边界情况测试 ===\n")
	
	// 负余额保护（理论上不会发生，但测试保护机制）
	fmt.Printf("1. 负余额保护测试:\n")
	edgeLease := &TestLease{
		ID:           "lease-edge",
		OwingBalance: -100.0, // 假设的负余额
		Status:       "active",
	}
	
	edgePayment := &TestPayment{Amount: 50.0}
	
	fmt.Printf("  初始余额: $%.2f (负数)\n", edgeLease.OwingBalance)
	fmt.Printf("  删除支付: $%.2f\n", edgePayment.Amount)
	
	err := simulateDeletePayment(edgeLease, edgePayment)
	if err == nil {
		fmt.Printf("  结果余额: $%.2f\n", edgeLease.OwingBalance)
		if edgeLease.OwingBalance >= 0 {
			fmt.Printf("  ✅ 负余额保护正常\n")
		} else {
			fmt.Printf("  ❌ 负余额保护失败\n")
		}
	}

	fmt.Printf("\n=== Scenario 4 测试完成 ===\n")
}

func main() {
	TestScenario4()
}
