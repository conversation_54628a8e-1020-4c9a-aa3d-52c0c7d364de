2025-07-31T11:38:17.024-04:00 level=INFO msg=Connected to MongoDB database, database="rr"
2025-07-31T11:38:17.027-04:00 level=INFO msg=Connected to MongoDB database, database="rr_test"
2025-07-31T11:38:17.029-04:00 level=INFO msg=Connected to MongoDB database, database="tmp"
2025-07-31T11:38:17.03-04:00 level=INFO msg=Registered predefined collection, database="rr", collName="mail_log", collection="mailLog"
2025-07-31T11:38:17.03-04:00 level=ERROR msg=failed to create collection, file="mongodb.go:184", error={}
2025-07-31T11:38:17.031-04:00 level=INFO msg=Encryption initialized, keyLength=32
2025-07-31T11:38:17.031-04:00 level=INFO msg=🚀 Starting Metro2 notification scheduler..., delayHours=0, delayMinutes=1, processIntervalMinutes=1, cleanupDays=30
2025-07-31T11:38:17.031-04:00 level=INFO, value="Metro2 notification cleanup worker started"
2025-07-31T11:38:17.031-04:00 level=INFO msg=Metro2 notification processing worker started, intervalMinutes=1
2025-07-31T11:38:17.032-04:00 level=INFO, value="📋 Processing Metro2 notification tasks"
2025-07-31T11:38:17.033-04:00 level=INFO, value="📭 No pending Metro2 notification tasks found"
