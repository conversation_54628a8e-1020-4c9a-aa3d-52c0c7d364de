package main

import (
	"fmt"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID                    string
	RentAmount            float64
	AdditionalMonthlyFees float64
	OwingBalance          float64
	Status                string
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
}

// 模拟 Scenario 2 的完整逻辑
func simulateAddPayment(lease *TestLease, payment *TestPayment) error {
	// Scenario 5: 只有active状态的租约才能接受支付
	if lease.Status != "active" {
		return fmt.Errorf("can only add payments to active leases, current status: %s", lease.Status)
	}

	// Scenario 2: 更新余额
	newBalance := lease.OwingBalance - payment.Amount
	if newBalance < 0 {
		newBalance = 0 // 余额不能为负
	}
	lease.OwingBalance = newBalance

	return nil
}

// TestScenario2Advanced 测试 Scenario 2 的高级功能
func TestScenario2Advanced() {
	fmt.Println("=== 测试 Scenario 2 高级功能：状态检查 + 余额更新 ===")

	// 测试案例：正常租约
	fmt.Printf("\n1. 测试正常租约支付:\n")
	activeLease := &TestLease{
		ID:                    "lease-1",
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          1200.0,
		Status:                "active",
	}

	payment1 := &TestPayment{
		ID:      "payment-1",
		LeaseID: "lease-1",
		Amount:  500.0,
	}

	fmt.Printf("  租约状态: %s\n", activeLease.Status)
	fmt.Printf("  支付前余额: $%.2f\n", activeLease.OwingBalance)
	fmt.Printf("  支付金额: $%.2f\n", payment1.Amount)

	err := simulateAddPayment(activeLease, payment1)
	if err != nil {
		fmt.Printf("  ❌ 支付失败: %v\n", err)
	} else {
		fmt.Printf("  支付后余额: $%.2f\n", activeLease.OwingBalance)
		if activeLease.OwingBalance == 700.0 {
			fmt.Printf("  ✅ 正常租约支付成功\n")
		} else {
			fmt.Printf("  ❌ 余额计算错误\n")
		}
	}

	// 测试案例：已结束租约
	fmt.Printf("\n2. 测试已结束租约支付:\n")
	endedLease := &TestLease{
		ID:                    "lease-2",
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          800.0,
		Status:                "ended",
	}

	payment2 := &TestPayment{
		ID:      "payment-2",
		LeaseID: "lease-2",
		Amount:  300.0,
	}

	fmt.Printf("  租约状态: %s\n", endedLease.Status)
	fmt.Printf("  支付前余额: $%.2f\n", endedLease.OwingBalance)
	fmt.Printf("  尝试支付: $%.2f\n", payment2.Amount)

	err = simulateAddPayment(endedLease, payment2)
	if err != nil {
		fmt.Printf("  ✅ 正确拒绝: %v\n", err)
	} else {
		fmt.Printf("  ❌ 应该拒绝已结束租约的支付\n")
	}

	// 测试案例：边界情况
	fmt.Printf("\n3. 测试边界情况:\n")

	// 3.1 零余额支付
	zeroLease := &TestLease{
		ID:           "lease-3",
		OwingBalance: 0.0,
		Status:       "active",
	}

	payment3 := &TestPayment{Amount: 100.0}

	fmt.Printf("  3.1 零余额租约支付 $%.2f:\n", payment3.Amount)
	fmt.Printf("    支付前余额: $%.2f\n", zeroLease.OwingBalance)

	err = simulateAddPayment(zeroLease, payment3)
	if err == nil {
		fmt.Printf("    支付后余额: $%.2f\n", zeroLease.OwingBalance)
		if zeroLease.OwingBalance == 0.0 {
			fmt.Printf("    ✅ 零余额保持为0\n")
		} else {
			fmt.Printf("    ❌ 零余额处理错误\n")
		}
	}

	// 3.2 精确支付
	exactLease := &TestLease{
		ID:           "lease-4",
		OwingBalance: 500.0,
		Status:       "active",
	}

	payment4 := &TestPayment{Amount: 500.0}

	fmt.Printf("  3.2 精确支付 $%.2f:\n", payment4.Amount)
	fmt.Printf("    支付前余额: $%.2f\n", exactLease.OwingBalance)

	err = simulateAddPayment(exactLease, payment4)
	if err == nil {
		fmt.Printf("    支付后余额: $%.2f\n", exactLease.OwingBalance)
		if exactLease.OwingBalance == 0.0 {
			fmt.Printf("    ✅ 精确支付清零余额\n")
		} else {
			fmt.Printf("    ❌ 精确支付处理错误\n")
		}
	}

	// 测试不同状态
	fmt.Printf("\n4. 测试不同租约状态:\n")
	statuses := []string{"active", "pending", "ended", "deleted"}

	for _, status := range statuses {
		testLease := &TestLease{
			ID:           fmt.Sprintf("lease-%s", status),
			OwingBalance: 1000.0,
			Status:       status,
		}

		testPayment := &TestPayment{Amount: 200.0}

		fmt.Printf("  状态 '%s': ", status)
		err = simulateAddPayment(testLease, testPayment)

		if status == "active" {
			if err == nil {
				fmt.Printf("✅ 允许支付 (余额: $%.2f)\n", testLease.OwingBalance)
			} else {
				fmt.Printf("❌ 不应该拒绝: %v\n", err)
			}
		} else {
			if err != nil {
				fmt.Printf("✅ 正确拒绝\n")
			} else {
				fmt.Printf("❌ 应该拒绝\n")
			}
		}
	}

	fmt.Printf("\n=== Scenario 2 高级测试完成 ===\n")
}

func main() {
	TestScenario2Advanced()
}
