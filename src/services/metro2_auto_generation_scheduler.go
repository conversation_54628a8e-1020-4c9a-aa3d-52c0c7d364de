package services

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomail"
)

// Metro2AutoGenerationConfig Metro2自动生成配置
type Metro2AutoGenerationConfig struct {
	Enabled                bool
	SendDay                int
	SendHour               int
	RecipientEmail         string
	ProcessIntervalMinutes int
	TestMode               bool
}

// getMetro2AutoGenerationConfig 获取Metro2自动生成配置
func getMetro2AutoGenerationConfig() Metro2AutoGenerationConfig {
	config := Metro2AutoGenerationConfig{
		Enabled:                true,
		SendDay:                21,
		SendHour:               9,
		RecipientEmail:         "<EMAIL>",
		ProcessIntervalMinutes: 60,
		TestMode:               false,
	}

	// 读取配置
	if enabled := goconfig.Config("metro2AutoGeneration.enabled"); enabled != nil {
		if enabledBool, ok := enabled.(bool); ok {
			config.Enabled = enabledBool
		}
	}

	if sendDay := goconfig.Config("metro2AutoGeneration.sendDay"); sendDay != nil {
		if dayInt, ok := sendDay.(int); ok {
			config.SendDay = dayInt
		} else if dayInt64, ok := sendDay.(int64); ok {
			config.SendDay = int(dayInt64)
		}
	}

	if sendHour := goconfig.Config("metro2AutoGeneration.sendHour"); sendHour != nil {
		if hourInt, ok := sendHour.(int); ok {
			config.SendHour = hourInt
		} else if hourInt64, ok := sendHour.(int64); ok {
			config.SendHour = int(hourInt64)
		}
	}

	if recipientEmail := goconfig.Config("metro2AutoGeneration.recipientEmail"); recipientEmail != nil {
		if emailStr, ok := recipientEmail.(string); ok && emailStr != "" {
			config.RecipientEmail = emailStr
		}
	}

	if processInterval := goconfig.Config("metro2AutoGeneration.processIntervalMinutes"); processInterval != nil {
		if intervalInt, ok := processInterval.(int); ok {
			config.ProcessIntervalMinutes = intervalInt
		} else if intervalInt64, ok := processInterval.(int64); ok {
			config.ProcessIntervalMinutes = int(intervalInt64)
		}
	}

	if testMode := goconfig.Config("metro2AutoGeneration.testMode"); testMode != nil {
		if testBool, ok := testMode.(bool); ok {
			config.TestMode = testBool
		}
	}

	return config
}

// StartMetro2AutoGenerationScheduler 启动Metro2自动生成调度器
func StartMetro2AutoGenerationScheduler() {
	config := getMetro2AutoGenerationConfig()

	if !config.Enabled {
		golog.Info("Metro2 auto generation scheduler is disabled")
		return
	}

	golog.Info("Starting Metro2 auto generation scheduler",
		"sendDay", config.SendDay,
		"sendHour", config.SendHour,
		"recipientEmail", config.RecipientEmail,
		"testMode", config.TestMode,
		"processIntervalMinutes", config.ProcessIntervalMinutes)

	// 启动处理任务的goroutine
	go startMetro2AutoGenerationWorker(config)
}

// startMetro2AutoGenerationWorker 启动Metro2自动生成处理工作器
func startMetro2AutoGenerationWorker(config Metro2AutoGenerationConfig) {
	golog.Info("Metro2 auto generation processing worker started",
		"intervalMinutes", config.ProcessIntervalMinutes,
		"testMode", config.TestMode,
		"sendDay", config.SendDay,
		"sendHour", config.SendHour)

	// 如果是测试模式，立即执行一次
	if config.TestMode {
		golog.Info("TEST MODE: Generating Metro2 files and sending email immediately")
		processMetro2AutoGenerationTask(config)
	} else {
		golog.Info("PRODUCTION MODE: Will generate on day %d at hour %d of each month", config.SendDay, config.SendHour)
	}

	// 然后按间隔执行
	ticker := time.NewTicker(time.Duration(config.ProcessIntervalMinutes) * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		if !config.TestMode {
			// 生产模式：检查是否是发送日期和时间
			now := time.Now()
			golog.Info("Checking if today is generation day and hour",
				"currentDay", now.Day(),
				"currentHour", now.Hour(),
				"sendDay", config.SendDay,
				"sendHour", config.SendHour)

			if now.Day() == config.SendDay && now.Hour() == config.SendHour {
				// 检查今天是否已经生成过（防重复机制）
				reportMonth := now.Format("2006-01")
				if !hasAutoGeneratedThisMonth(context.Background(), reportMonth) {
					golog.Info("Generating Metro2 files and sending email for today")
					processMetro2AutoGenerationTask(config)
				} else {
					golog.Info("Already generated Metro2 files for this month, skipping")
				}
			} else {
				golog.Info("Not generation day/hour yet, waiting...")
			}
		}
	}
}

// hasAutoGeneratedThisMonth 检查当月是否已经自动生成过Metro2文件
func hasAutoGeneratedThisMonth(ctx context.Context, reportMonth string) bool {
	// 这里可以查询metro2_generation_logs表来检查
	// 为了简化，暂时返回false，实际实现时可以添加数据库查询
	return false
}

// processMetro2AutoGenerationTask 处理Metro2自动生成任务
func processMetro2AutoGenerationTask(config Metro2AutoGenerationConfig) {
	ctx := context.Background()

	golog.Info("Processing Metro2 auto generation task")

	// 获取当前月份作为报告月份
	now := time.Now()
	reportMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 使用admin用户ID进行生成（需要从配置或数据库获取）
	adminUserID := "admin" // 这里需要实际的admin用户ID

	golog.Info("Generating Metro2 files",
		"reportMonth", reportMonth.Format("2006-01"),
		"adminUserID", adminUserID)

	// 生成Metro2文件数据
	jsonData, generationLog, err := entities.GenerateMetro2FileData(ctx, reportMonth, adminUserID)
	if err != nil {
		golog.Error("Failed to generate Metro2 file data", "error", err)
		return
	}

	// 生成Metro2文件
	metro2File, err := utils.GenerateMetro2File(string(jsonData))
	if err != nil {
		golog.Error("Failed to generate Metro2 file", "error", err)
		return
	}

	golog.Info("Metro2 files generated successfully",
		"jsonDataSize", len(jsonData),
		"metro2FileSize", len(metro2File),
		"totalLeases", generationLog.TotalLeases,
		"totalTenants", generationLog.TotalTenants)

	// 创建ZIP文件
	zipData, err := createMetro2ZipFile(jsonData, metro2File, reportMonth)
	if err != nil {
		golog.Error("Failed to create ZIP file", "error", err)
		return
	}

	// 发送邮件
	err = sendMetro2AutoGenerationEmail(config.RecipientEmail, zipData, reportMonth, generationLog)
	if err != nil {
		golog.Error("Failed to send Metro2 auto generation email", "error", err)
		return
	}

	// 保存生成日志到数据库
	generationLog.FileName = fmt.Sprintf("Metro2-Auto-Generated-%s.zip", reportMonth.Format("2006-01"))
	generationLog.FileSize = int64(len(zipData))
	if err := generationLog.Create(ctx); err != nil {
		golog.Error("Failed to save Metro2 generation log", "error", err)
		// 继续执行，不因为日志保存失败而中断
	}

	golog.Info("Completed Metro2 auto generation task successfully",
		"reportMonth", reportMonth.Format("2006-01"),
		"zipFileSize", len(zipData),
		"recipientEmail", config.RecipientEmail)
}

// createMetro2ZipFile 创建包含Metro2文件和JSON文件的ZIP文件
func createMetro2ZipFile(jsonData, metro2File []byte, reportMonth time.Time) ([]byte, error) {
	var buf bytes.Buffer
	zipWriter := zip.NewWriter(&buf)

	// 生成文件名
	monthStr := reportMonth.Format("2006-01")
	timestamp := time.Now().Format("20060102-150405")

	// 添加Metro2文件到ZIP
	metro2FileName := fmt.Sprintf("RM-Metro2-%s-%s.txt", monthStr, timestamp)
	metro2FileWriter, err := zipWriter.Create(metro2FileName)
	if err != nil {
		return nil, fmt.Errorf("failed to create Metro2 file in ZIP: %v", err)
	}
	_, err = metro2FileWriter.Write(metro2File)
	if err != nil {
		return nil, fmt.Errorf("failed to write Metro2 file to ZIP: %v", err)
	}

	// 添加JSON文件到ZIP
	jsonFileName := fmt.Sprintf("RM-Metro2-Backup-%s-%s.json", monthStr, timestamp)
	jsonFileWriter, err := zipWriter.Create(jsonFileName)
	if err != nil {
		return nil, fmt.Errorf("failed to create JSON file in ZIP: %v", err)
	}
	_, err = jsonFileWriter.Write(jsonData)
	if err != nil {
		return nil, fmt.Errorf("failed to write JSON file to ZIP: %v", err)
	}

	// 关闭ZIP writer
	err = zipWriter.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close ZIP writer: %v", err)
	}

	return buf.Bytes(), nil
}

// sendMetro2AutoGenerationEmail 发送Metro2自动生成邮件
func sendMetro2AutoGenerationEmail(recipientEmail string, zipData []byte, reportMonth time.Time, generationLog *entities.Metro2GenerationLog) error {
	// 生成邮件主题
	subject := fmt.Sprintf("Metro2 Monthly Report - %s", reportMonth.Format("January 2006"))

	// 生成邮件内容
	htmlBody := fmt.Sprintf(`
		<h2>Metro2 Monthly Report - %s</h2>

		<p>Dear Administrator,</p>

		<p>The Metro2 monthly report has been automatically generated and is attached to this email.</p>

		<h3>Report Summary:</h3>
		<ul>
			<li><strong>Report Month:</strong> %s</li>
			<li><strong>Generated At:</strong> %s</li>
			<li><strong>Total Leases Processed:</strong> %d</li>
			<li><strong>Total Tenants:</strong> %d</li>
			<li><strong>Total Payments:</strong> %d</li>
		</ul>

		<h3>Files Included:</h3>
		<ul>
			<li>Metro2 Report File (.txt) - Standard Metro2 format for credit bureau submission</li>
			<li>JSON Backup File (.json) - Detailed data backup for internal records</li>
		</ul>

		<p>Please review the attached files and submit the Metro2 report to the appropriate credit bureaus as needed.</p>

		<p>If you have any questions or concerns about this report, please contact the system administrator.</p>

		<p>Best regards,<br/>
		Report Rentals System<br/>
		Automated Metro2 Generation Service</p>

		<hr>
		<p style="font-size: 12px; color: #666;">
			This is an automated email generated by the Report Rentals system.
			Generated on %s.
		</p>
	`,
		reportMonth.Format("January 2006"),
		reportMonth.Format("2006-01"),
		time.Now().Format("2006-01-02 15:04:05 MST"),
		generationLog.TotalLeases,
		generationLog.TotalTenants,
		generationLog.TotalPayments,
		time.Now().Format("2006-01-02 15:04:05 MST"))

	// 获取邮件发送器
	mailer, err := gomail.GetSendMailObj()
	if err != nil {
		return fmt.Errorf("failed to get mailer: %v", err)
	}

	// 创建临时文件用于附件
	tempDir := os.TempDir()
	zipFileName := fmt.Sprintf("Metro2-Report-%s.zip", reportMonth.Format("2006-01"))
	tempFilePath := filepath.Join(tempDir, zipFileName)

	// 写入临时文件
	err = os.WriteFile(tempFilePath, zipData, 0644)
	if err != nil {
		return fmt.Errorf("failed to write temporary ZIP file: %v", err)
	}

	// 确保在函数结束时删除临时文件
	defer func() {
		if err := os.Remove(tempFilePath); err != nil {
			golog.Warn("Failed to remove temporary ZIP file", "path", tempFilePath, "error", err)
		}
	}()

	// 创建邮件消息
	msg := &gomail.EmailMessage{
		To:          []string{recipientEmail},
		Subject:     subject,
		HTML:        htmlBody,
		Engine:      "gmail",
		Attachments: []string{tempFilePath},
	}

	// 发送邮件
	err = mailer.SendMail("gmail", msg)
	if err != nil {
		return fmt.Errorf("failed to send email: %v", err)
	}

	golog.Info("Metro2 auto generation email sent successfully",
		"recipientEmail", recipientEmail,
		"subject", subject,
		"zipFileSize", len(zipData),
		"reportMonth", reportMonth.Format("2006-01"))

	return nil
}
