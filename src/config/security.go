package config

import (
	"os"
	"strconv"
	"time"
)

// SecurityConfig 安全配置结构
type SecurityConfig struct {
	// JWT配置
	JWTSecret          string
	JWTExpirationHours int
	JWTRefreshExpHours int

	// 密码策略
	MinPasswordLength  int
	RequireSpecialChar bool
	RequireNumber      bool
	RequireUppercase   bool

	// 会话配置
	SessionTimeout   time.Duration
	MaxLoginAttempts int
	LockoutDuration  time.Duration

	// API安全
	RateLimitPerMinute int
	MaxRequestSize     int64

	// CORS配置
	AllowedOrigins []string
	AllowedMethods []string
	AllowedHeaders []string

	// 数据库安全
	DBConnectionTimeout time.Duration
	DBMaxConnections    int

	// 文件上传安全
	MaxFileSize       int64
	AllowedFileTypes  []string
	UploadPath        string
	MaxFilesPerEntity int

	// 加密配置
	EncryptionKey string
	HashCost      int

	// 用户限制配置
	UserLimits UserLimitsConfig
}

// UserLimitsConfig 用户限制配置结构
type UserLimitsConfig struct {
	FreeUserMaxProperties int
	FreeUserMaxLeases     int
}

// LoadSecurityConfig 从环境变量加载安全配置
func LoadSecurityConfig() *SecurityConfig {
	config := &SecurityConfig{
		// JWT配置
		JWTSecret:          getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
		JWTExpirationHours: getEnvAsInt("JWT_EXPIRATION_HOURS", 24),
		JWTRefreshExpHours: getEnvAsInt("JWT_REFRESH_EXPIRATION_HOURS", 168), // 7天

		// 密码策略
		MinPasswordLength:  getEnvAsInt("MIN_PASSWORD_LENGTH", 8),
		RequireSpecialChar: getEnvAsBool("REQUIRE_SPECIAL_CHAR", true),
		RequireNumber:      getEnvAsBool("REQUIRE_NUMBER", true),
		RequireUppercase:   getEnvAsBool("REQUIRE_UPPERCASE", true),

		// 会话配置
		SessionTimeout:   time.Duration(getEnvAsInt("SESSION_TIMEOUT_MINUTES", 30)) * time.Minute,
		MaxLoginAttempts: getEnvAsInt("MAX_LOGIN_ATTEMPTS", 5),
		LockoutDuration:  time.Duration(getEnvAsInt("LOCKOUT_DURATION_MINUTES", 15)) * time.Minute,

		// API安全
		RateLimitPerMinute: getEnvAsInt("RATE_LIMIT_PER_MINUTE", 100),
		MaxRequestSize:     getEnvAsInt64("MAX_REQUEST_SIZE", 10*1024*1024), // 10MB

		// CORS配置
		AllowedOrigins: []string{
			getEnv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8080"),
		},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{
			"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With",
		},

		// 数据库安全
		DBConnectionTimeout: time.Duration(getEnvAsInt("DB_CONNECTION_TIMEOUT_SECONDS", 30)) * time.Second,
		DBMaxConnections:    getEnvAsInt("DB_MAX_CONNECTIONS", 20),

		// 文件上传安全 - 使用 goupload 支持更大文件
		MaxFileSize: getEnvAsInt64("MAX_FILE_SIZE", 50*1024*1024), // 50MB (提升自10MB)
		AllowedFileTypes: []string{
			".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".gif",
		},
		UploadPath:        getEnv("UPLOAD_PATH", "./uploads"),
		MaxFilesPerEntity: getEnvAsInt("MAX_FILES_PER_ENTITY", 10), // 每个实体最多10个文件

		// 加密配置
		EncryptionKey: getEnv("ENCRYPTION_KEY", "your-encryption-key-change-in-production"),
		HashCost:      getEnvAsInt("HASH_COST", 12),

		// 用户限制配置
		UserLimits: UserLimitsConfig{
			FreeUserMaxProperties: getEnvAsInt("FREE_USER_MAX_PROPERTIES", 3),
			FreeUserMaxLeases:     getEnvAsInt("FREE_USER_MAX_LEASES", 3),
		},
	}

	return config
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为int
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsInt64 获取环境变量并转换为int64
func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool 获取环境变量并转换为bool
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// ValidatePassword 验证密码是否符合安全策略
func (c *SecurityConfig) ValidatePassword(password string) error {
	if len(password) < c.MinPasswordLength {
		return &PasswordError{
			Message: "密码长度不足",
			Code:    "PASSWORD_TOO_SHORT",
		}
	}

	if c.RequireUppercase && !hasUppercase(password) {
		return &PasswordError{
			Message: "密码必须包含大写字母",
			Code:    "PASSWORD_NO_UPPERCASE",
		}
	}

	if c.RequireNumber && !hasNumber(password) {
		return &PasswordError{
			Message: "密码必须包含数字",
			Code:    "PASSWORD_NO_NUMBER",
		}
	}

	if c.RequireSpecialChar && !hasSpecialChar(password) {
		return &PasswordError{
			Message: "密码必须包含特殊字符",
			Code:    "PASSWORD_NO_SPECIAL_CHAR",
		}
	}

	return nil
}

// PasswordError 密码错误类型
type PasswordError struct {
	Message string
	Code    string
}

func (e *PasswordError) Error() string {
	return e.Message
}

// 辅助函数
func hasUppercase(s string) bool {
	for _, r := range s {
		if r >= 'A' && r <= 'Z' {
			return true
		}
	}
	return false
}

func hasNumber(s string) bool {
	for _, r := range s {
		if r >= '0' && r <= '9' {
			return true
		}
	}
	return false
}

func hasSpecialChar(s string) bool {
	specialChars := "!@#$%^&*()_+-=[]{}|;:,.<>?"
	for _, r := range s {
		for _, sc := range specialChars {
			if r == sc {
				return true
			}
		}
	}
	return false
}
