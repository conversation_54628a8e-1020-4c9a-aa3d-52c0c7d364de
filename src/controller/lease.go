package controller

import (
	"fmt"
	"net/http"
	"os"
	"rent_report/config"
	"rent_report/entities"
	"rent_report/middleware"
	"rent_report/router"
	"rent_report/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
)

type LeaseController struct{}

func init() {
	router.Register(&LeaseController{})
}

func (c *LeaseController) RegisterRoutes(r *gin.Engine) {
	leaseRouter := r.Group("/v1/leases")
	{
		leaseRouter.GET("", c.handleGetLeases)
		leaseRouter.GET("/check-limit", c.handleCheckLeaseLimit)
		leaseRouter.POST("", middleware.RequireLandlord(), c.handleCreateLease)
		leaseRouter.GET("/:leaseId", middleware.SmartResourceAuth("lease"), c.handleGetLease)
		leaseRouter.PUT("/:leaseId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleUpdateLease)
		leaseRouter.GET("/:leaseId/check-deletion", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleCheckLeaseDeletion)
		leaseRouter.DELETE("/:leaseId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleDeleteLease)
		// Note: tenant routes are now in lease_tenant.go

		leaseRouter.POST("/:leaseId/document", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleUploadDocument)
		leaseRouter.DELETE("/:leaseId/document", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleDeleteDocument)
		leaseRouter.DELETE("/:leaseId/document/:documentId", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleDeleteSpecificDocument)
		leaseRouter.GET("/:leaseId/documents/deleted", middleware.SmartResourceAuth("lease"), c.handleGetDeletedDocuments)
		leaseRouter.PUT("/:leaseId/documents/:documentId/restore", middleware.RequireLandlord(), middleware.SmartResourceAuth("lease"), c.handleRestoreDocument)

		// Deprecated route (for backward compatibility)
		leaseRouter.GET("/download/:fileId", c.handleDownloadDocument)
		// 测试端点 - 生产环境中应该注释掉
		// leaseRouter.POST("/test-auto-payment", c.handleTestAutoPayment)
		// leaseRouter.GET("/test-auto-payment-simple", c.handleTestAutoPaymentSimple)
	}

	// RESTful document download
	r.GET("/v1/documents/:fileId", c.handleDownloadDocument)
}

func (c *LeaseController) handleCheckLeaseLimit(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Check user lease limit
	securityConfig := config.LoadSecurityConfig()
	if err := utils.CheckUserLeaseLimit(ctx.Request.Context(), userID, securityConfig.UserLimits.FreeUserMaxLeases); err != nil {
		if limitErr, ok := err.(*utils.UserLimitError); ok {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error":   "limit_reached",
				"type":    limitErr.Type,
				"current": limitErr.Current,
				"limit":   limitErr.Limit,
				"message": limitErr.Message,
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check user limits"})
		return
	}

	// No limit reached
	ctx.JSON(http.StatusOK, gin.H{"canCreate": true})
}

func (c *LeaseController) handleGetLeases(ctx *gin.Context) {
	limit := 10 // Default limit

	if limitStr := ctx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get query parameters
	filters := map[string]string{
		"propertyId": ctx.Query("propertyId"),
		"status":     ctx.Query("status"),
		"userId":     userID,
		"role":       ctx.Query("role"),
		"invId":      ctx.Query("invId"),
	}

	leases, total, err := entities.GetLeases(ctx.Request.Context(), limit, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"items": leases,
		"total": total,
	})
}

func (c *LeaseController) handleCreateLease(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Check user lease limit before creating
	securityConfig := config.LoadSecurityConfig()
	if err := utils.CheckUserLeaseLimit(ctx.Request.Context(), userID, securityConfig.UserLimits.FreeUserMaxLeases); err != nil {
		if limitErr, ok := err.(*utils.UserLimitError); ok {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error":   "limit_reached",
				"type":    limitErr.Type,
				"current": limitErr.Current,
				"limit":   limitErr.Limit,
				"message": limitErr.Message,
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check user limits"})
		return
	}

	var lease entities.Lease
	if err := ctx.ShouldBindJSON(&lease); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// 验证要使用的room状态是否为vacant
	property, err := entities.GetProperty(ctx.Request.Context(), lease.PropertyID, userID)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Property not found: %v", err)})
		return
	}

	room, err := property.GetRoom(lease.RoomID)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Room not found: %v", err)})
		return
	}

	if room.Status != "vacant" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Room is not vacant"})
		return
	}

	lease.UserID = userID
	lease.ID = utils.GenerateNanoID()

	// 新创建的租约默认启用自动支付
	lease.AutoPay = true

	if err := lease.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新room状态为occupied
	room.Status = "occupied"
	if err := property.UpdateRoom(*room); err != nil {
		// 记录错误但不中断流程
		ctx.JSON(http.StatusCreated, gin.H{
			"lease": lease,
			"error": fmt.Sprintf("Lease created but failed to update room status: %v", err),
		})
		return
	}

	// 保存更新后的property
	if err := property.Update(ctx.Request.Context()); err != nil {
		// 记录错误但不中断流程
		ctx.JSON(http.StatusCreated, gin.H{
			"lease": lease,
			"error": fmt.Sprintf("Lease created but failed to save property changes: %v", err),
		})
		return
	}

	ctx.JSON(http.StatusCreated, lease)
}

func (c *LeaseController) handleGetLease(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")

	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, lease)
}

func (c *LeaseController) handleUpdateLease(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")

	// 先获取原始lease信息
	originalLease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var lease entities.Lease
	if err := ctx.ShouldBindJSON(&lease); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if lease.ID != leaseId {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Lease ID mismatch"})
		return
	}

	lease.UserID = userID

	// 保留原始lease中已被软删除的文档，防止前端数据覆盖软删除状态
	// 创建一个map来跟踪前端发送的文档ID，避免重复
	frontendDocIds := make(map[string]bool)
	for _, doc := range lease.Documents {
		frontendDocIds[doc.ID] = true
	}

	// 对于前端发送的已删除文档，保留原始的deletedAt字段
	for i, doc := range lease.Documents {
		if doc.Status == "deleted" {
			// 查找原始文档中对应的deletedAt字段
			for _, originalDoc := range originalLease.Documents {
				if originalDoc.ID == doc.ID && originalDoc.Status == "deleted" && originalDoc.DeletedAt != nil {
					lease.Documents[i].DeletedAt = originalDoc.DeletedAt
					break
				}
			}
		}
	}

	// 只添加前端没有发送的已删除文档
	for _, originalDoc := range originalLease.Documents {
		if originalDoc.Status == "deleted" && !frontendDocIds[originalDoc.ID] {
			lease.Documents = append(lease.Documents, originalDoc)
		}
	}

	if err := lease.Update(ctx.Request.Context()); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 检查lease状态是否从active变为ended或deleted，或从ended变为active
	if originalLease.Status == "active" && (lease.Status == "ended" || lease.Status == "deleted") {
		// 写入当前日期到 EndDate 字段
		lease.EndDate = time.Now().Format("2006-01-02")
	}
	if originalLease.Status == "ended" && lease.Status == "active" {
		lease.EndDate = ""
	}

	// 强制同步 endDt 字段到数据库
	updateFields := bson.M{}
	if originalLease.Status == "active" && (lease.Status == "ended" || lease.Status == "deleted") {
		updateFields["endDt"] = lease.EndDate
	}
	if originalLease.Status == "ended" && lease.Status == "active" {
		updateFields["endDt"] = ""
	}
	if len(updateFields) > 0 {
		leaseColl := gomongo.Coll("rr", "leases")
		if leaseColl != nil {
			_, _ = leaseColl.UpdateOne(ctx.Request.Context(), bson.M{"_id": leaseId}, bson.M{"$set": updateFields})
		}
	}

	// 检查lease状态是否从active变为其他状态
	if originalLease.Status == "active" && lease.Status != "active" {
		// 更新room状态为vacant
		property, err := entities.GetProperty(ctx.Request.Context(), lease.PropertyID, userID)
		if err == nil {
			room, err := property.GetRoom(lease.RoomID)
			if err == nil {
				room.Status = "vacant"
				if err := property.UpdateRoom(*room); err == nil {
					_ = property.Update(ctx.Request.Context()) // 忽略错误，主流程不受影响
				}
			}
		}
	}

	ctx.JSON(http.StatusOK, lease)
}

func (c *LeaseController) handleDeleteLease(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")

	// 获取lease信息
	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 删除lease
	if err := lease.Delete(ctx.Request.Context()); err != nil {
		// 检查是否是约束错误
		if strings.Contains(err.Error(), "cannot delete lease") {
			ctx.JSON(http.StatusConflict, gin.H{
				"error": err.Error(),
				"code":  "DELETION_CONSTRAINT_VIOLATION",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 更新room状态为vacant
	property, err := entities.GetProperty(ctx.Request.Context(), lease.PropertyID, userID)
	if err == nil {
		room, err := property.GetRoom(lease.RoomID)
		if err == nil {
			room.Status = "vacant"
			if err := property.UpdateRoom(*room); err == nil {
				_ = property.Update(ctx.Request.Context()) // 忽略错误，主流程不受影响
			}
		}
	}

	ctx.Status(http.StatusNoContent)
}

func (c *LeaseController) handleCheckLeaseDeletion(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get leaseId from URL
	leaseId := ctx.Param("leaseId")

	// 检查删除约束
	if err := entities.CheckLeaseDeletionConstraints(ctx.Request.Context(), leaseId, userID); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		// 检查是否是约束错误
		if strings.Contains(err.Error(), "cannot delete lease") {
			ctx.JSON(http.StatusConflict, gin.H{
				"canDelete": false,
				"error":     err.Error(),
				"code":      "DELETION_CONSTRAINT_VIOLATION",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 没有约束，可以删除
	ctx.JSON(http.StatusOK, gin.H{"canDelete": true})
}

func (c *LeaseController) handleUpdateLeaseTenant(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")
	tenantId := ctx.Param("tenantId")

	var tenant entities.Tenant
	if err := ctx.ShouldBindJSON(&tenant); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	tenant.ID = tenantId
	tenant.LeaseID = leaseId
	tenant.UserID = userID

	if err := entities.UpdateLeaseTenantAndSync(ctx.Request.Context(), leaseId, userID, tenant); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, tenant)
}

func (c *LeaseController) handleDeleteLeaseTenant(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")
	tenantId := ctx.Param("tenantId")

	// 使用 SoftDeleteLeaseTenant 函数来软删除租客
	if err := entities.SoftDeleteLeaseTenant(ctx.Request.Context(), leaseId, userID, tenantId); err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		if err.Error() == "tenant not found in ctnts" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Tenant not found in this lease"})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (c *LeaseController) handleUploadDocument(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")

	// 先获取原始lease信息
	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否是lease的所有者（landlord）
	if lease.UserID != userID {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only lease owner can upload documents"})
		return
	}

	// Scenario 5: Check if lease allows file upload
	if lease.Status != "active" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": fmt.Sprintf("cannot upload files to non-active lease (status: %s)", lease.Status)})
		return
	}

	// 加载安全配置
	securityConfig := config.LoadSecurityConfig()

	// 检查当前有效文件数量是否已达到限制（排除deleted和final_deleted状态的文件）
	activeFileCount := 0
	for _, doc := range lease.Documents {
		if doc.Status != "deleted" && doc.Status != "final_deleted" {
			activeFileCount++
		}
	}
	if activeFileCount >= securityConfig.MaxFilesPerEntity {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Maximum number of files (%d) reached", securityConfig.MaxFilesPerEntity),
		})
		return
	}

	// 处理文件上传 - 与 Property 保持一致
	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// 验证文件大小
	if header.Size > securityConfig.MaxFileSize {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("File size exceeds maximum allowed size of %d bytes", securityConfig.MaxFileSize),
		})
		return
	}

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.LeaseDocuments, statsColl)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create stats updater: %v", err)})
		return
	}

	// 使用 goupload 上传文件
	result, err := goupload.Upload(
		ctx.Request.Context(),
		statsUpdater,                // statsUpdater
		uploadConfig.Site,           // site (从配置文件读取)
		uploadConfig.LeaseDocuments, // entryName (从配置文件读取)
		userID,                      // uid
		file,                        // reader
		header.Filename,             // originalFilename
		header.Size,                 // clientDeclaredSize
	)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to upload file: %v", err)})
		return
	}

	// 创建新的文档记录
	newDocument := entities.LeaseDocument{
		ID:            utils.GenerateNanoID(),
		FilePath:      result.Path,
		FileName:      header.Filename,
		GeneratedName: result.Filename,
		FileSize:      result.Size,
		FileType:      result.MimeType,
		UploadedAt:    time.Now(),
		UploadedBy:    userID,
		Status:        "normal", // 默认状态为normal
	}

	// 添加到lease的documents数组中
	lease.Documents = append(lease.Documents, newDocument)

	// 使用 Update 方法，与 Property 保持一致
	if err := lease.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update lease with file info"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "Document uploaded successfully",
		"document":       newDocument,
		"totalDocuments": len(lease.Documents),
	})
}

func (c *LeaseController) handleDownloadDocument(ctx *gin.Context) {
	fileID := ctx.Param("fileId")

	// 查找包含该文件的租约
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取用户信息以检查权限
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// 查找文档信息
	var document *entities.LeaseDocument

	// 如果是管理员，搜索所有租约
	if user.Role == entities.RoleAdmin {
		// 管理员可以访问所有文档，直接从数据库搜索
		leaseColl := gomongo.Coll("rr", "leases")
		if leaseColl == nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
			return
		}

		// 搜索包含该文档ID的租约
		cursor, err := leaseColl.Find(ctx.Request.Context(), bson.M{
			"documents.id": fileID,
		})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search leases"})
			return
		}
		defer cursor.Close(ctx.Request.Context())

		for cursor.Next(ctx.Request.Context()) {
			var lease entities.Lease
			if err := cursor.Decode(&lease); err != nil {
				continue
			}

			for _, doc := range lease.Documents {
				if doc.ID == fileID {
					document = &doc
					break
				}
			}
			if document != nil {
				break
			}
		}
	} else {
		// 普通用户：使用中间件层面的权限逻辑来查找文档
		leaseColl := gomongo.Coll("rr", "leases")
		if leaseColl == nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
			return
		}

		// 搜索包含该文档ID的所有租约
		cursor, err := leaseColl.Find(ctx.Request.Context(), bson.M{
			"documents.id": fileID,
		})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search leases"})
			return
		}
		defer cursor.Close(ctx.Request.Context())

		// 对每个包含该文档的租约，使用中间件权限逻辑检查访问权限
		for cursor.Next(ctx.Request.Context()) {
			var lease entities.Lease
			if err := cursor.Decode(&lease); err != nil {
				continue
			}

			// 使用与SmartResourceAuth相同的权限检查逻辑
			hasAccess := false

			// 检查是否是房东
			if lease.UserID == user.ID {
				hasAccess = true
			}

			// 检查是否是主租客（兼容旧数据）
			if !hasAccess && lease.TenantID == user.ID {
				hasAccess = true
			}

			// 检查是否在当前租客列表中
			if !hasAccess {
				for _, t := range lease.CurrentTenants {
					if t.ID == user.ID || t.TenantId == user.ID {
						hasAccess = true
						break
					}
				}
			}

			// 检查是否在过往租客列表中
			if !hasAccess {
				for _, t := range lease.PastTenants {
					if t.ID == user.ID || t.TenantId == user.ID {
						hasAccess = true
						break
					}
				}
			}

			// 如果有权限访问这个租约，查找文档
			if hasAccess {
				for _, doc := range lease.Documents {
					if doc.ID == fileID {
						document = &doc
						break
					}
				}
				if document != nil {
					break
				}
			}
		}
	}

	if document == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// 检查 FilePath 是否为空
	if document.FilePath == "" {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File path is empty"})
		return
	}

	// 构建完整的文件路径 - FilePath 是 goupload 格式的相对路径
	fullPath := "./uploads/lease_documents/" + document.FilePath

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found on disk"})
		return
	}

	// 读取文件
	fileData, err := os.ReadFile(fullPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read file"})
		return
	}

	// 设置响应头并返回文件
	ctx.Header("Content-Disposition", "attachment; filename="+document.FileName)
	ctx.Header("Content-Type", document.FileType)
	ctx.Data(http.StatusOK, document.FileType, fileData)
}

func (c *LeaseController) handleDeleteDocument(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")

	// 获取lease信息并验证权限
	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否是lease的所有者（landlord）
	if lease.UserID != userID {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only lease owner can delete documents"})
		return
	}

	// 检查是否有文件需要删除（只计算非deleted状态的文件）
	normalDocuments := 0
	for _, doc := range lease.Documents {
		if doc.Status != "deleted" {
			normalDocuments++
		}
	}
	if normalDocuments == 0 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "No documents found for this lease"})
		return
	}

	deletedCount := 0

	// 检查是否有受保护的文档
	for _, doc := range lease.Documents {
		if doc.Status != "deleted" && doc.IsProtected {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error": "Cannot delete protected document. One or more documents are protected due to Metro2 reporting requirements.",
				"code":  "DOCUMENT_PROTECTED",
			})
			return
		}
	}

	// 软删除：将所有非deleted状态的文档标记为deleted
	now := time.Now()
	for i := range lease.Documents {
		if lease.Documents[i].Status != "deleted" {
			lease.Documents[i].Status = "deleted"
			lease.Documents[i].DeletedAt = &now
			deletedCount++
		}
	}

	// 更新lease记录
	if err := lease.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update lease record"})
		return
	}

	response := gin.H{
		"success":      true,
		"message":      "All documents marked as deleted successfully",
		"deletedCount": deletedCount,
	}

	ctx.JSON(http.StatusOK, response)
}

// handleTestAutoPayment 测试自动支付功能 (已注释，生产环境不使用)
/*
func (c *LeaseController) handleTestAutoPayment(ctx *gin.Context) {
	// 验证用户已登录（但不限制具体权限，因为这是系统级操作）
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Please login first"})
		return
	}

	// 手动触发自动支付处理（系统级操作，处理所有符合条件的租约）
	err = scheduler.ProcessAutoPaymentsManually()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   err.Error(),
			"message": "Auto payment processing failed",
		})
		return
	}

	// 获取统计信息
	stats, err := scheduler.GetAutoPaymentStats(ctx.Request.Context())
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"message":     "Auto payment processing completed, but failed to get stats",
			"triggerUser": userID,
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":     "Auto payment processing completed successfully",
		"triggerUser": userID,
		"stats":       stats,
	})
}
*/

// handleTestAutoPaymentSimple 简单的自动支付测试（无需认证）(已注释，生产环境不使用)
/*
func (c *LeaseController) handleTestAutoPaymentSimple(ctx *gin.Context) {
	// 完全不需要认证的测试端点，用于调试
	ctx.JSON(http.StatusOK, gin.H{
		"message": "Auto payment test endpoint is working",
		"time":    time.Now().Format("2006-01-02 15:04:05"),
	})

	// 手动触发自动支付处理
	err := scheduler.ProcessAutoPaymentsManually()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":   err.Error(),
			"message": "Auto payment processing failed",
		})
		return
	}

	// 获取统计信息
	stats, err := scheduler.GetAutoPaymentStats(ctx.Request.Context())
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"message": "Auto payment processing completed, but failed to get stats",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "Auto payment processing completed successfully",
		"stats":   stats,
	})
}
*/

// handleDeleteSpecificDocument 删除租约的特定文档
func (c *LeaseController) handleDeleteSpecificDocument(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")
	documentId := ctx.Param("documentId")

	// 获取lease信息并验证权限
	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否是lease的所有者（landlord）
	if lease.UserID != userID {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only lease owner can delete documents"})
		return
	}

	// 查找要删除的文档
	var documentToDelete *entities.LeaseDocument
	var documentIndex int = -1
	for i, doc := range lease.Documents {
		if doc.ID == documentId {
			documentToDelete = &doc
			documentIndex = i
			break
		}
	}

	if documentToDelete == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		return
	}

	// 检查文档是否已经被删除
	if documentToDelete.Status == "deleted" {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Document already deleted"})
		return
	}

	// 检查文档是否受保护
	if documentToDelete.IsProtected {
		ctx.JSON(http.StatusForbidden, gin.H{
			"error": "Cannot delete protected document. This document is protected due to Metro2 reporting requirements.",
			"code":  "DOCUMENT_PROTECTED",
		})
		return
	}

	// 软删除：将文档状态设置为deleted并设置删除时间
	now := time.Now()
	lease.Documents[documentIndex].Status = "deleted"
	lease.Documents[documentIndex].DeletedAt = &now

	// 更新lease记录
	if err := lease.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update lease record"})
		return
	}

	// 计算剩余的非deleted文档数量
	remainingDocuments := 0
	for _, doc := range lease.Documents {
		if doc.Status != "deleted" {
			remainingDocuments++
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":            true,
		"message":            "Document marked as deleted successfully",
		"deletedDocument":    documentToDelete,
		"remainingDocuments": remainingDocuments,
	})
}

// handleGetDeletedDocuments 获取已删除的文档（回收站）
func (c *LeaseController) handleGetDeletedDocuments(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")

	// 获取lease信息并验证权限
	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 过滤已删除的文档（14天内且状态为deleted）
	var deletedDocuments []map[string]interface{}
	now := time.Now()
	fourteenDaysAgo := now.AddDate(0, 0, -14)

	for _, doc := range lease.Documents {
		if doc.Status == "deleted" && doc.DeletedAt != nil {
			// 只显示14天内删除的文件
			if doc.DeletedAt.After(fourteenDaysAgo) {
				// 计算剩余天数
				expiryDate := doc.DeletedAt.AddDate(0, 0, 14)
				daysLeft := int(expiryDate.Sub(now).Hours() / 24)
				if daysLeft < 0 {
					daysLeft = 0
				}

				deletedDoc := map[string]interface{}{
					"id":            doc.ID,
					"fileName":      doc.FileName,
					"fileSize":      doc.FileSize,
					"fileType":      doc.FileType,
					"uploadedAt":    doc.UploadedAt,
					"deletedAt":     doc.DeletedAt,
					"daysLeft":      daysLeft,
					"generatedName": doc.GeneratedName,
				}
				deletedDocuments = append(deletedDocuments, deletedDoc)
			}
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":   true,
		"documents": deletedDocuments,
		"total":     len(deletedDocuments),
	})
}

// handleRestoreDocument 恢复已删除的文档
func (c *LeaseController) handleRestoreDocument(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	leaseId := ctx.Param("leaseId")
	documentId := ctx.Param("documentId")

	// 获取lease信息并验证权限
	lease, err := entities.GetLease(ctx.Request.Context(), leaseId, userID)
	if err != nil {
		if err.Error() == "lease not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 检查用户是否是lease的所有者（landlord）
	if lease.UserID != userID {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only lease owner can restore documents"})
		return
	}

	// 查找要恢复的文档
	var documentIndex int = -1
	for i, doc := range lease.Documents {
		if doc.ID == documentId {
			documentIndex = i
			break
		}
	}

	if documentIndex == -1 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		return
	}

	document := &lease.Documents[documentIndex]

	// 检查文档状态
	if document.Status != "deleted" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Document is not deleted"})
		return
	}

	// 检查是否在14天期限内
	if document.DeletedAt != nil {
		now := time.Now()
		fourteenDaysAgo := now.AddDate(0, 0, -14)
		if document.DeletedAt.Before(fourteenDaysAgo) {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Document has expired and cannot be restored"})
			return
		}
	}

	// 加载安全配置
	securityConfig := config.LoadSecurityConfig()

	// 检查恢复后是否会超过文件数量限制（排除deleted和final_deleted状态的文件）
	// 注意：前端会处理文件数量限制的UI显示，这里不再返回错误
	activeFileCount := 0
	for _, doc := range lease.Documents {
		if doc.Status != "deleted" && doc.Status != "final_deleted" {
			activeFileCount++
		}
	}
	if activeFileCount >= securityConfig.MaxFilesPerEntity {
		// 静默返回，不恢复文件，让前端处理UI显示
		ctx.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "File limit reached",
		})
		return
	}

	// 检查是否有同名的活跃文件
	for _, doc := range lease.Documents {
		if doc.ID != documentId && doc.FileName == document.FileName && doc.Status != "deleted" && doc.Status != "final_deleted" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Cannot restore file. A file with the name '%s' already exists", document.FileName),
				"code":  "DUPLICATE_FILE_NAME",
			})
			return
		}
	}

	// 恢复文档：设置状态为normal并清除deletedAt
	lease.Documents[documentIndex].Status = "normal"
	lease.Documents[documentIndex].DeletedAt = nil

	// 更新lease记录
	if err := lease.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update lease record"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":          true,
		"message":          "Document restored successfully",
		"restoredDocument": document,
	})
}
