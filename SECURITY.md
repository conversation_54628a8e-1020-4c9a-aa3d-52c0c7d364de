# 安全配置文档

## 概述

本项目已实现完整的安全权限验证系统，包括：

1. **智能资源权限验证** - 基于业务逻辑的权限控制
2. **安全中间件** - 添加安全头和其他保护措施
3. **配置管理** - 通过环境变量管理敏感信息
4. **输入验证** - 防止恶意输入

## 权限验证系统

### 智能资源权限验证

系统实现了基于现有业务逻辑的智能权限验证：

#### 房产访问权限
- **房东**: 可以访问自己拥有的房产
- **租客**: 可以访问与自己租约关联的房产
- **管理员**: 可以访问所有房产

#### 租约访问权限
- **房东**: 可以访问自己创建的租约
- **租客**: 可以访问自己参与的租约
- **管理员**: 可以访问所有租约

#### 租客访问权限
- **房东**: 可以访问自己租约中的租客
- **租客**: 可以访问自己的信息
- **管理员**: 可以访问所有租客

### 使用方法

在路由中应用权限验证中间件：

```go
// 房产相关路由
propertyRouter.GET("/:propertyId", middleware.SmartResourceAuth("property"), c.handleGetProperty)
propertyRouter.PUT("/:propertyId", middleware.SmartResourceAuth("property"), c.handleUpdateProperty)
propertyRouter.DELETE("/:propertyId", middleware.SmartResourceAuth("property"), c.handleDeleteProperty)

// 租约相关路由
leaseRouter.GET("/:leaseId", middleware.SmartResourceAuth("lease"), c.handleGetLease)
leaseRouter.PUT("/:leaseId", middleware.SmartResourceAuth("lease"), c.handleUpdateLease)

// 租客相关路由
tenantRouter.GET("/:tenantId", middleware.SmartResourceAuth("tenant"), c.handleGetTenant)
tenantRouter.PUT("/:tenantId", middleware.SmartResourceAuth("tenant"), c.handleUpdateTenant)

// 管理员权限
adminRouter.Use(middleware.RequireAdmin())

// 房东权限
landlordRouter.Use(middleware.RequireLandlord())

// 租客权限
tenantRouter.Use(middleware.RequireTenant())
```

## 安全中间件

### 已实现的安全中间件

1. **SecurityHeaders** - 添加安全头
   - X-Frame-Options: DENY
   - X-Content-Type-Options: nosniff
   - X-XSS-Protection: 1; mode=block
   - Content-Security-Policy
   - Strict-Transport-Security

2. **CORS** - 跨域资源共享控制
3. **RateLimit** - 速率限制
4. **RequestSizeLimit** - 请求大小限制
5. **InputValidation** - 输入验证
6. **Logging** - 安全日志记录

### 全局应用

所有安全中间件已在主程序中全局应用：

```go
func applySecurityMiddleware(r *gin.Engine, securityConfig *config.SecurityConfig) {
    r.Use(middleware.SecurityHeaders())
    r.Use(middleware.CORS(securityConfig))
    r.Use(middleware.RateLimit(securityConfig))
    r.Use(middleware.RequestSizeLimit(securityConfig))
    r.Use(middleware.InputValidation())
    r.Use(middleware.Logging())
    r.Use(middleware.SecureCookies())
}
```

## 环境变量配置

### 必需的环境变量

```bash
# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRATION_HOURS=24

# 数据库配置
MONGODB_URI=********************************:port/database

# 加密配置
ENCRYPTION_KEY=your-encryption-key-change-in-production

# Stripe配置
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
```

### 可选的环境变量

```bash
# 密码策略
MIN_PASSWORD_LENGTH=8
REQUIRE_SPECIAL_CHAR=true
REQUIRE_NUMBER=true
REQUIRE_UPPERCASE=true

# API安全
RATE_LIMIT_PER_MINUTE=100
MAX_REQUEST_SIZE=10485760

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# 文件上传
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
```

## 安全最佳实践

### 1. 生产环境配置

- 使用强密码和密钥
- 启用HTTPS
- 配置正确的CORS策略
- 设置适当的速率限制
- 启用安全日志记录

### 2. 数据库安全

- 使用环境变量存储数据库连接信息
- 限制数据库用户权限
- 定期备份数据
- 监控数据库访问

### 3. API安全

- 所有敏感操作都需要权限验证
- 使用HTTPS传输
- 实施速率限制
- 验证所有输入

### 4. 文件上传安全

- 限制文件大小
- 验证文件类型
- 扫描恶意文件
- 存储在安全位置

## 安全测试

### 权限测试

1. **普通用户测试**
   - 验证用户只能访问自己的资源
   - 验证用户无法访问其他用户的资源

2. **管理员测试**
   - 验证管理员可以访问所有资源
   - 验证管理员权限不被绕过

3. **跨用户测试**
   - 验证房东和租客的权限边界
   - 验证关联资源的访问权限

### 安全头测试

使用浏览器开发者工具或curl命令检查安全头：

```bash
curl -I http://localhost:8089/v1/properties
```

应该看到以下安全头：
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Content-Security-Policy: ...

## 故障排除

### 常见问题

1. **权限被拒绝**
   - 检查用户角色和权限
   - 验证资源关联关系
   - 检查中间件配置

2. **CORS错误**
   - 检查ALLOWED_ORIGINS配置
   - 验证前端域名设置

3. **速率限制**
   - 检查RATE_LIMIT_PER_MINUTE设置
   - 考虑增加限制或使用Redis

4. **文件上传失败**
   - 检查文件大小限制
   - 验证文件类型
   - 确认上传路径权限

## 更新日志

### v1.0.0
- 实现智能资源权限验证
- 添加安全中间件
- 创建安全配置管理
- 完善输入验证

## 联系信息

如有安全问题，请联系开发团队。 