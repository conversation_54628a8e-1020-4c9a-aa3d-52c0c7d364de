package controller

import (
	"fmt"
	"net/http"
	"os"
	"rent_report/config"
	"rent_report/entities"
	"rent_report/middleware"
	"rent_report/router"
	"rent_report/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
	"go.mongodb.org/mongo-driver/bson"
)

type PropertyController struct{}

func init() {
	router.Register(&PropertyController{})
}

func (c *PropertyController) RegisterRoutes(r *gin.Engine) {
	propertyRouter := r.Group("/v1/properties")
	{
		propertyRouter.GET("", c.handleGetProperties)
		propertyRouter.GET("/check-limit", c.handleCheckPropertyLimit)
		propertyRouter.POST("", c.handleCreateProperty)
		propertyRouter.GET("/:propertyId", middleware.SmartResourceAuth("property"), c.handleGetProperty)
		propertyRouter.PUT("/:propertyId", middleware.SmartResourceAuth("property"), c.handleUpdateProperty)
		propertyRouter.GET("/:propertyId/check-deletion", middleware.SmartResourceAuth("property"), c.handleCheckPropertyDeletion)
		propertyRouter.DELETE("/:propertyId", middleware.SmartResourceAuth("property"), c.handleDeleteProperty)
		propertyRouter.POST("/:propertyId/document", middleware.SmartResourceAuth("property"), c.handleUploadDocument)
		propertyRouter.DELETE("/:propertyId/document", middleware.SmartResourceAuth("property"), c.handleDeleteDocument)
		propertyRouter.DELETE("/:propertyId/document/:documentId", middleware.SmartResourceAuth("property"), c.handleDeleteSpecificDocument)
		propertyRouter.GET("/:propertyId/documents/deleted", middleware.SmartResourceAuth("property"), c.handleGetDeletedDocuments)
		propertyRouter.PUT("/:propertyId/documents/:documentId/restore", middleware.SmartResourceAuth("property"), c.handleRestoreDocument)
		propertyRouter.GET("/download/:fileId", c.handleDownloadDocument)
		propertyRouter.POST("/:propertyId/archive", middleware.SmartResourceAuth("property"), c.handleArchiveProperty)
	}
}

func (c *PropertyController) handleGetProperties(ctx *gin.Context) {
	limit := 10 // Default limit

	if limitStr := ctx.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get query parameters
	filters := map[string]string{
		"name":       ctx.Query("name"),
		"propertyId": ctx.Query("propertyId"),
		"status":     ctx.Query("status"),
		"userId":     userID,
	}

	properties, total, err := entities.GetProperties(ctx.Request.Context(), limit, filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"items": properties,
		"total": total,
	})
}

func (c *PropertyController) handleCheckPropertyLimit(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Check user property limit
	securityConfig := config.LoadSecurityConfig()
	if err := utils.CheckUserPropertyLimit(ctx.Request.Context(), userID, securityConfig.UserLimits.FreeUserMaxProperties); err != nil {
		if limitErr, ok := err.(*utils.UserLimitError); ok {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error":   "limit_reached",
				"type":    limitErr.Type,
				"current": limitErr.Current,
				"limit":   limitErr.Limit,
				"message": limitErr.Message,
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check user limits"})
		return
	}

	// No limit reached
	ctx.JSON(http.StatusOK, gin.H{"canCreate": true})
}

func (c *PropertyController) handleCreateProperty(ctx *gin.Context) {
	// Get userID from token first
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Check user property limit before creating
	securityConfig := config.LoadSecurityConfig()
	if err := utils.CheckUserPropertyLimit(ctx.Request.Context(), userID, securityConfig.UserLimits.FreeUserMaxProperties); err != nil {
		if limitErr, ok := err.(*utils.UserLimitError); ok {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error":   "limit_reached",
				"type":    limitErr.Type,
				"current": limitErr.Current,
				"limit":   limitErr.Limit,
				"message": limitErr.Message,
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check user limits"})
		return
	}

	var property entities.Property
	if err := ctx.ShouldBindJSON(&property); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Set the userID from the token
	property.UserID = userID
	property.ID = utils.GenerateNanoID()

	if err := property.Create(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, property)
}

func (c *PropertyController) handleGetProperty(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId from URL
	propertyId := ctx.Param("propertyId")

	property, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, property)
}

func (c *PropertyController) handleUpdateProperty(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId from URL
	propertyId := ctx.Param("propertyId")

	// 先获取原始property信息
	originalProperty, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var property entities.Property
	if err := ctx.ShouldBindJSON(&property); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Ensure the property ID matches the URL
	if property.ID != propertyId {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Property ID mismatch"})
		return
	}

	// Set the userID from the token
	property.UserID = userID

	// 保留原始property中已被软删除的文档，防止前端数据覆盖软删除状态
	// 创建一个map来跟踪前端发送的文档ID，避免重复
	frontendDocIds := make(map[string]bool)
	for _, doc := range property.Documents {
		frontendDocIds[doc.ID] = true
	}

	// 对于前端发送的已删除文档，保留原始的deletedAt字段
	for i, doc := range property.Documents {
		if doc.Status == "deleted" {
			// 查找原始文档中对应的deletedAt字段
			for _, originalDoc := range originalProperty.Documents {
				if originalDoc.ID == doc.ID && originalDoc.Status == "deleted" && originalDoc.DeletedAt != nil {
					property.Documents[i].DeletedAt = originalDoc.DeletedAt
					break
				}
			}
		}
	}

	// 只添加前端没有发送的已删除文档
	for _, originalDoc := range originalProperty.Documents {
		if originalDoc.Status == "deleted" && !frontendDocIds[originalDoc.ID] {
			property.Documents = append(property.Documents, originalDoc)
		}
	}

	if err := property.Update(ctx.Request.Context()); err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, property)
}

func (c *PropertyController) handleDeleteProperty(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId from URL
	propertyId := ctx.Param("propertyId")

	if err := entities.DeleteProperty(ctx.Request.Context(), propertyId, userID); err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		// 检查是否是约束错误（包含"cannot delete property"的错误）
		if strings.Contains(err.Error(), "cannot delete property") {
			ctx.JSON(http.StatusConflict, gin.H{
				"error": err.Error(),
				"code":  "DELETION_CONSTRAINT_VIOLATION",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

func (c *PropertyController) handleCheckPropertyDeletion(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId from URL
	propertyId := ctx.Param("propertyId")

	// 检查删除约束
	if err := entities.CheckPropertyDeletionConstraints(ctx.Request.Context(), propertyId, userID); err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		// 检查是否是约束错误
		if strings.Contains(err.Error(), "cannot delete property") {
			ctx.JSON(http.StatusConflict, gin.H{
				"canDelete": false,
				"error":     err.Error(),
				"code":      "DELETION_CONSTRAINT_VIOLATION",
			})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 没有约束，可以删除
	ctx.JSON(http.StatusOK, gin.H{"canDelete": true})
}

// handleUploadDocument 处理文件上传
func (c *PropertyController) handleUploadDocument(ctx *gin.Context) {
	// 获取userID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取propertyId
	propertyId := ctx.Param("propertyId")

	// 获取property
	property, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 加载安全配置
	securityConfig := config.LoadSecurityConfig()

	// 检查当前有效文件数量是否已达到限制（排除deleted和final_deleted状态的文件）
	activeFileCount := 0
	for _, doc := range property.Documents {
		if doc.Status != "deleted" && doc.Status != "final_deleted" {
			activeFileCount++
		}
	}
	if activeFileCount >= securityConfig.MaxFilesPerEntity {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("Maximum number of files (%d) reached", securityConfig.MaxFilesPerEntity),
		})
		return
	}

	// 处理文件上传
	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// 验证文件大小
	if header.Size > securityConfig.MaxFileSize {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("File size exceeds maximum allowed size of %d bytes", securityConfig.MaxFileSize),
		})
		return
	}

	// 加载上传配置
	uploadConfig := config.LoadUploadConfig()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := goupload.NewStatsUpdater(uploadConfig.Site, uploadConfig.PropertyDocuments, statsColl)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create stats updater: %v", err)})
		return
	}

	// 使用 goupload 上传文件
	result, err := goupload.Upload(
		ctx.Request.Context(),
		statsUpdater,                   // statsUpdater
		uploadConfig.Site,              // site (从配置文件读取)
		uploadConfig.PropertyDocuments, // entryName (从配置文件读取)
		userID,                         // uid
		file,                           // reader
		header.Filename,                // originalFilename
		header.Size,                    // clientDeclaredSize
	)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to upload file: %v", err)})
		return
	}

	// 创建新的文档记录
	newDocument := entities.PropertyDocument{
		ID:            utils.GenerateNanoID(),
		FilePath:      result.Path,
		FileName:      header.Filename,
		GeneratedName: result.Filename,
		FileSize:      result.Size,
		FileType:      result.MimeType,
		UploadedAt:    time.Now(),
		UploadedBy:    userID,
		Status:        "normal", // 默认状态为normal
	}

	// 添加到property的documents数组中
	property.Documents = append(property.Documents, newDocument)

	// 保存更新后的property
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update property"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":        true,
		"message":        "Document uploaded successfully",
		"document":       newDocument,
		"totalDocuments": len(property.Documents),
	})
}

// handleDeleteDocument 处理property文档删除
func (c *PropertyController) handleDeleteDocument(ctx *gin.Context) {
	// 获取userID
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取propertyId
	propertyId := ctx.Param("propertyId")

	// 获取property
	property, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 检查是否有文件需要删除（只计算非deleted状态的文件）
	normalDocuments := 0
	for _, doc := range property.Documents {
		if doc.Status != "deleted" {
			normalDocuments++
		}
	}
	if normalDocuments == 0 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "No documents found for this property"})
		return
	}

	deletedCount := 0

	// 检查是否有受保护的文档
	for _, doc := range property.Documents {
		if doc.Status != "deleted" && doc.IsProtected {
			ctx.JSON(http.StatusForbidden, gin.H{
				"error": "Cannot delete protected document. One or more documents are protected due to Metro2 reporting requirements.",
				"code":  "DOCUMENT_PROTECTED",
			})
			return
		}
	}

	// 软删除：将所有非deleted状态的文档标记为deleted
	now := time.Now()
	for i := range property.Documents {
		if property.Documents[i].Status != "deleted" {
			property.Documents[i].Status = "deleted"
			property.Documents[i].DeletedAt = &now
			deletedCount++
		}
	}

	// 更新property记录
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update property record"})
		return
	}

	response := gin.H{
		"success":      true,
		"message":      "All documents marked as deleted successfully",
		"deletedCount": deletedCount,
	}

	ctx.JSON(http.StatusOK, response)
}

// handleDownloadDocument 处理 property 文件下载
func (c *PropertyController) handleDownloadDocument(ctx *gin.Context) {
	fileID := ctx.Param("fileId")

	// 查找包含该文件的物业
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// 获取用户信息以检查权限
	user, err := entities.GetUserByID(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// 查找文档信息
	var document *entities.PropertyDocument

	// 如果是管理员，搜索所有物业
	if user.Role == entities.RoleAdmin {
		// 管理员可以访问所有文档，直接从数据库搜索
		propertyColl := gomongo.Coll("rr", "properties")
		if propertyColl == nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Database not available"})
			return
		}

		// 搜索包含该文档ID的物业
		cursor, err := propertyColl.Find(ctx.Request.Context(), bson.M{
			"documents.id": fileID,
		})
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search properties"})
			return
		}
		defer cursor.Close(ctx.Request.Context())

		for cursor.Next(ctx.Request.Context()) {
			var property entities.Property
			if err := cursor.Decode(&property); err != nil {
				continue
			}

			for _, doc := range property.Documents {
				if doc.ID == fileID {
					document = &doc
					break
				}
			}
			if document != nil {
				break
			}
		}
	} else {
		// 普通用户：只能访问自己的物业
		filters := map[string]string{
			"userId": userID,
		}
		properties, _, err := entities.GetProperties(ctx.Request.Context(), 1000, filters)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get properties"})
			return
		}

		// 查找包含该文件ID的文档
		for _, propertyList := range properties {
			// 获取完整的物业信息
			fullProperty, err := entities.GetProperty(ctx.Request.Context(), propertyList.ID, userID)
			if err != nil {
				continue
			}

			for _, doc := range fullProperty.Documents {
				if doc.ID == fileID {
					document = &doc
					break
				}
			}
			if document != nil {
				break
			}
		}
	}

	if document == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// 检查 FilePath 是否为空
	if document.FilePath == "" {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File path is empty"})
		return
	}

	// 构建完整的文件路径 - FilePath 是 goupload 格式的相对路径
	fullPath := "./uploads/property_documents/" + document.FilePath

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "File not found on disk"})
		return
	}

	// 读取文件
	fileData, err := os.ReadFile(fullPath)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read file"})
		return
	}

	// 设置响应头并返回文件
	ctx.Header("Content-Disposition", "attachment; filename="+document.FileName)
	ctx.Header("Content-Type", document.FileType)
	ctx.Data(http.StatusOK, document.FileType, fileData)
}

// handleArchiveProperty 处理property归档，批量更新相关leases
func (c *PropertyController) handleArchiveProperty(ctx *gin.Context) {
	// Get userID from token
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Get propertyId from URL
	propertyId := ctx.Param("propertyId")

	// 验证property是否存在且属于当前用户
	_, err = entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 批量更新该property下的所有active leases
	err = entities.ArchivePropertyLeases(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to archive property leases: " + err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":    "Property archived successfully",
		"propertyId": propertyId,
	})
}

// handleDeleteSpecificDocument 删除房产的特定文档
func (c *PropertyController) handleDeleteSpecificDocument(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	propertyId := ctx.Param("propertyId")
	documentId := ctx.Param("documentId")

	// 获取property信息并验证权限
	property, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 查找要删除的文档
	var documentToDelete *entities.PropertyDocument
	var documentIndex int = -1
	for i, doc := range property.Documents {
		if doc.ID == documentId {
			documentToDelete = &doc
			documentIndex = i
			break
		}
	}

	if documentToDelete == nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		return
	}

	// 检查文档是否已经被删除
	if documentToDelete.Status == "deleted" {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Document already deleted"})
		return
	}

	// 检查文档是否受保护
	if documentToDelete.IsProtected {
		ctx.JSON(http.StatusForbidden, gin.H{
			"error": "Cannot delete protected document. This document is protected due to Metro2 reporting requirements.",
			"code":  "DOCUMENT_PROTECTED",
		})
		return
	}

	// 软删除：将文档状态设置为deleted并设置删除时间
	now := time.Now()
	property.Documents[documentIndex].Status = "deleted"
	property.Documents[documentIndex].DeletedAt = &now

	// 更新property记录
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update property record"})
		return
	}

	// 计算剩余的非deleted文档数量
	remainingDocuments := 0
	for _, doc := range property.Documents {
		if doc.Status != "deleted" {
			remainingDocuments++
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":            true,
		"message":            "Document marked as deleted successfully",
		"deletedDocument":    documentToDelete,
		"remainingDocuments": remainingDocuments,
	})
}

// handleGetDeletedDocuments 获取已删除的文档（回收站）
func (c *PropertyController) handleGetDeletedDocuments(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	propertyId := ctx.Param("propertyId")

	// 获取property信息并验证权限
	property, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 过滤已删除的文档（14天内且状态为deleted）
	var deletedDocuments []map[string]interface{}
	now := time.Now()
	fourteenDaysAgo := now.AddDate(0, 0, -14)

	for _, doc := range property.Documents {
		if doc.Status == "deleted" && doc.DeletedAt != nil {
			// 只显示14天内删除的文件
			if doc.DeletedAt.After(fourteenDaysAgo) {
				// 计算剩余天数
				expiryDate := doc.DeletedAt.AddDate(0, 0, 14)
				daysLeft := int(expiryDate.Sub(now).Hours() / 24)
				if daysLeft < 0 {
					daysLeft = 0
				}

				deletedDoc := map[string]interface{}{
					"id":            doc.ID,
					"fileName":      doc.FileName,
					"fileSize":      doc.FileSize,
					"fileType":      doc.FileType,
					"uploadedAt":    doc.UploadedAt,
					"deletedAt":     doc.DeletedAt,
					"daysLeft":      daysLeft,
					"generatedName": doc.GeneratedName,
				}
				deletedDocuments = append(deletedDocuments, deletedDoc)
			}
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":   true,
		"documents": deletedDocuments,
		"total":     len(deletedDocuments),
	})
}

// handleRestoreDocument 恢复已删除的文档
func (c *PropertyController) handleRestoreDocument(ctx *gin.Context) {
	userID, err := utils.GetUserIDFromToken(ctx.Request)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	propertyId := ctx.Param("propertyId")
	documentId := ctx.Param("documentId")

	// 获取property信息并验证权限
	property, err := entities.GetProperty(ctx.Request.Context(), propertyId, userID)
	if err != nil {
		if err.Error() == "property not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 查找要恢复的文档
	var documentIndex int = -1
	for i, doc := range property.Documents {
		if doc.ID == documentId {
			documentIndex = i
			break
		}
	}

	if documentIndex == -1 {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Document not found"})
		return
	}

	document := &property.Documents[documentIndex]

	// 检查文档状态
	if document.Status != "deleted" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Document is not deleted"})
		return
	}

	// 检查是否在14天期限内
	if document.DeletedAt != nil {
		now := time.Now()
		fourteenDaysAgo := now.AddDate(0, 0, -14)
		if document.DeletedAt.Before(fourteenDaysAgo) {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Document has expired and cannot be restored"})
			return
		}
	}

	// 加载安全配置
	securityConfig := config.LoadSecurityConfig()

	// 检查恢复后是否会超过文件数量限制（排除deleted和final_deleted状态的文件）
	// 注意：前端会处理文件数量限制的UI显示，这里不再返回错误
	activeFileCount := 0
	for _, doc := range property.Documents {
		if doc.Status != "deleted" && doc.Status != "final_deleted" {
			activeFileCount++
		}
	}
	if activeFileCount >= securityConfig.MaxFilesPerEntity {
		// 静默返回，不恢复文件，让前端处理UI显示
		ctx.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "File limit reached",
		})
		return
	}

	// 检查是否有同名的活跃文件
	for _, doc := range property.Documents {
		if doc.ID != documentId && doc.FileName == document.FileName && doc.Status != "deleted" && doc.Status != "final_deleted" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Cannot restore file. A file with the name '%s' already exists", document.FileName),
				"code":  "DUPLICATE_FILE_NAME",
			})
			return
		}
	}

	// 恢复文档：设置状态为normal并清除deletedAt
	property.Documents[documentIndex].Status = "normal"
	property.Documents[documentIndex].DeletedAt = nil

	// 更新property记录
	if err := property.Update(ctx.Request.Context()); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update property record"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"success":          true,
		"message":          "Document restored successfully",
		"restoredDocument": document,
	})
}
