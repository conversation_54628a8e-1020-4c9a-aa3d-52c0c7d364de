package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/real-rm/gomongo"
	"github.com/real-rm/goupload"
)

func main() {
	fmt.Println("Testing goupload Delete method...")

	// 初始化 MongoDB
	gomongo.InitMongoDB()

	// 创建 context
	ctx := context.Background()

	// 创建统计更新器
	statsColl := gomongo.Coll("tmp", "dir_stats")
	site := "TEST" // 测试环境使用硬编码值
	statsUpdater, err := goupload.NewStatsUpdater(site, "property_documents", statsColl)
	if err != nil {
		log.Fatalf("Failed to create stats updater: %v", err)
	}

	// 测试参数
	entryName := "property_documents"
	relativePath := "100/2e3b2/v2Kqj0dDuu7x7GXpLvvFH.png"

	fmt.Printf("Attempting to delete file:\n")
	fmt.Printf("  Site: %s\n", site)
	fmt.Printf("  EntryName: %s\n", entryName)
	fmt.Printf("  RelativePath: %s\n", relativePath)
	fmt.Println()

	// 调用删除方法
	result, err := goupload.Delete(ctx, statsUpdater, site, entryName, relativePath)
	if err != nil {
		log.Printf("Delete failed: %v", err)
		return
	}

	// 输出结果
	fmt.Println("Delete operation completed successfully!")
	fmt.Printf("Result: %+v\n", result)

	// 详细输出删除结果
	if result != nil {
		fmt.Printf("Delete result details:\n")
		fmt.Printf("  Path: %s\n", result.Path)
		fmt.Printf("  IsPartialDelete: %v\n", result.IsPartialDelete)
		fmt.Printf("  DeletedPaths count: %d\n", len(result.DeletedPaths))
		for i, dp := range result.DeletedPaths {
			fmt.Printf("    [%d] Type: %s, Path: %s\n", i, dp.Type, dp.Path)
		}
		fmt.Printf("  FailedPaths count: %d\n", len(result.FailedPaths))
		for i, fp := range result.FailedPaths {
			fmt.Printf("    [%d] Type: %s, Path: %s\n", i, fp.Type, fp.Path)
		}
	}

	// 检查文件是否真的被删除
	fmt.Println("\nChecking if file still exists...")
	filePath := "uploads/property_documents/100/2e3b2/v2Kqj0dDuu7x7GXpLvvFH.png"
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fmt.Printf("✓ File %s has been deleted\n", filePath)
	} else if err != nil {
		fmt.Printf("✗ Error checking file %s: %v\n", filePath, err)
	} else {
		fmt.Printf("✗ File %s still exists!\n", filePath)
	}
}
