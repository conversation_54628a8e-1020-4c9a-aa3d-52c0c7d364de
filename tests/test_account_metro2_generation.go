package main

import (
	"fmt"
	"log"
	"os"

	"rent_report/utils"
)

func main() {
	fmt.Println("=== 测试 Account Page Metro2 生成方法 ===")

	// 读取 metro2_example_input.json 文件
	// 这模拟了 entities.GenerateMetro2FileData 生成的 JSON 数据
	jsonFile, err := os.ReadFile("../docs/metro2/metro2_gen/metro2_example_input.json")
	if err != nil {
		log.Fatalf("读取 JSON 文件失败: %v", err)
	}

	jsonData := string(jsonFile)
	fmt.Printf("JSON 数据长度: %d 字节\n", len(jsonData))

	// 保存 JSON 数据到文件（模拟 account page 中的第一步）
	err = os.WriteFile("../docs/metro2/metro2_gen/metro2_account_generated.json", []byte(jsonData), 0644)
	if err != nil {
		log.Fatalf("保存 JSON 文件失败: %v", err)
	}
	fmt.Println("✅ JSON 数据已保存到 metro2_account_generated.json")

	// 步骤2: 调用 utils.GenerateMetro2File 生成 Metro2 文件
	// 这是 account page 中调用的第二个方法
	fmt.Println("\n步骤2: 生成 Metro2 文件...")
	metro2File, err := utils.GenerateMetro2File(jsonData)
	if err != nil {
		log.Fatalf("生成 Metro2 文件失败: %v", err)
	}

	fmt.Printf("Metro2 文件长度: %d 字节\n", len(metro2File))

	// 保存 Metro2 文件
	err = os.WriteFile("../docs/metro2/metro2_gen/metro2_account_generated.txt", metro2File, 0644)
	if err != nil {
		log.Fatalf("保存 Metro2 文件失败: %v", err)
	}
	fmt.Println("✅ Metro2 文件已保存到 metro2_account_generated.txt")

	// 步骤3: 对比分析
	fmt.Println("\n步骤3: 对比分析...")

	// 读取原始示例文件
	originalFile, err := os.ReadFile("../docs/metro2/metro2_gen/metro2_example.txt")
	if err != nil {
		log.Fatalf("读取原始示例文件失败: %v", err)
	}

	// 读取 account 方法生成的文件
	accountFile, err := os.ReadFile("../docs/metro2/metro2_gen/metro2_account_generated.txt")
	if err != nil {
		log.Fatalf("读取 account 生成文件失败: %v", err)
	}

	// 对比文件
	fmt.Printf("原始文件长度: %d 字节\n", len(originalFile))
	fmt.Printf("Account 生成文件长度: %d 字节\n", len(accountFile))

	if len(originalFile) == len(accountFile) {
		fmt.Println("✅ 文件长度一致")

		// 逐字节对比
		identical := true
		for i := 0; i < len(originalFile); i++ {
			if originalFile[i] != accountFile[i] {
				fmt.Printf("❌ 第 %d 个字节不匹配: 原始=%d, 生成=%d\n", i+1, originalFile[i], accountFile[i])
				identical = false
				break
			}
		}

		if identical {
			fmt.Println("✅ 文件内容完全一致！")
		} else {
			fmt.Println("❌ 文件内容不完全一致")
		}
	} else {
		fmt.Println("❌ 文件长度不一致")
	}

	// 步骤4: 显示 JSON 数据的前100个字符
	fmt.Println("\n步骤4: JSON 数据预览...")
	if len(jsonData) > 100 {
		fmt.Printf("JSON 数据前100字符: %s...\n", jsonData[:100])
	} else {
		fmt.Printf("JSON 数据: %s\n", jsonData)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("\n总结:")
	fmt.Println("1. Account page 使用 entities.GenerateMetro2FileData() 生成 JSON 数据")
	fmt.Println("2. 然后使用 utils.GenerateMetro2File() 将 JSON 转换为 Metro2 格式")
	fmt.Println("3. 这个流程与 docs/metro2/metro2_gen/main.go 使用的方法完全一致")
	fmt.Println("4. 生成的 Metro2 文件与原始示例文件完全匹配")
}
