package middleware

import (
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestSecureCookiesMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name        string
		setupEnv    func()
		setupReq    func(*http.Request)
		expectHTTPS bool
		expectProd  bool
	}{
		{
			name: "Development HTTP",
			setupEnv: func() {
				os.Unsetenv("GIN_MODE")
				os.Unsetenv("ENVIRONMENT")
			},
			setupReq:    func(req *http.Request) {},
			expectHTTPS: false,
			expectProd:  false,
		},
		{
			name: "Production HTTPS",
			setupEnv: func() {
				os.Setenv("GIN_MODE", "release")
				os.Setenv("ENVIRONMENT", "production")
			},
			setupReq: func(req *http.Request) {
				req.Header.Set("X-Forwarded-Proto", "https")
			},
			expectHTTPS: true,
			expectProd:  true,
		},
		{
			name: "Development with HTTPS",
			setupEnv: func() {
				os.Unsetenv("GIN_MODE")
				os.Unsetenv("ENVIRONMENT")
			},
			setupReq: func(req *http.Request) {
				req.Header.Set("X-Forwarded-Proto", "https")
			},
			expectHTTPS: true,
			expectProd:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup environment
			tt.setupEnv()
			defer func() {
				os.Unsetenv("GIN_MODE")
				os.Unsetenv("ENVIRONMENT")
			}()

			// Create test router with SecureCookies middleware
			r := gin.New()
			r.Use(SecureCookies())

			// Add test route that sets a cookie
			r.GET("/test", func(c *gin.Context) {
				// Set an insecure cookie (no HttpOnly, no Secure)
				c.SetCookie("test_cookie", "test_value", 3600, "/", "", false, false)
				c.JSON(200, gin.H{"status": "ok"})
			})

			// Create test request
			req := httptest.NewRequest("GET", "/test", nil)
			tt.setupReq(req)

			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			// Check response
			if w.Code != 200 {
				t.Errorf("Expected status 200, got %d", w.Code)
				return
			}

			// Check Set-Cookie header
			cookies := w.Header()["Set-Cookie"]
			if len(cookies) == 0 {
				t.Error("Expected Set-Cookie header, but none found")
				return
			}

			cookieStr := strings.Join(cookies, "; ")
			t.Logf("Cookie string: %s", cookieStr)

			// HttpOnly should always be added
			if !strings.Contains(cookieStr, "HttpOnly") {
				t.Error("Cookie should have HttpOnly attribute")
			}

			// Secure should be added in HTTPS or production
			if tt.expectHTTPS || tt.expectProd {
				if !strings.Contains(cookieStr, "Secure") {
					t.Error("Cookie should have Secure attribute in HTTPS/production")
				}
			}

			// SameSite should be added
			if tt.expectProd {
				if !strings.Contains(cookieStr, "SameSite=Strict") {
					t.Error("Cookie should have SameSite=Strict in production")
				}
			} else {
				if !strings.Contains(cookieStr, "SameSite=Lax") {
					t.Error("Cookie should have SameSite=Lax in development")
				}
			}
		})
	}
}

func TestSecureCookiesDoesNotDuplicate(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create test router
	r := gin.New()
	r.Use(SecureCookies())

	// Add test route that sets a cookie with some security attributes already
	r.GET("/test", func(c *gin.Context) {
		// Manually set cookie with some attributes
		c.Header("Set-Cookie", "test_cookie=test_value; Path=/; HttpOnly; SameSite=Lax")
		c.JSON(200, gin.H{"status": "ok"})
	})

	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// Check that attributes are not duplicated
	cookies := w.Header()["Set-Cookie"]
	if len(cookies) == 0 {
		t.Error("Expected Set-Cookie header")
		return
	}

	cookieStr := strings.Join(cookies, "; ")
	t.Logf("Cookie string: %s", cookieStr)

	// Count occurrences of HttpOnly
	httpOnlyCount := strings.Count(cookieStr, "HttpOnly")
	if httpOnlyCount != 1 {
		t.Errorf("Expected HttpOnly to appear once, but appeared %d times", httpOnlyCount)
	}

	// Count occurrences of SameSite
	sameSiteCount := strings.Count(cookieStr, "SameSite")
	if sameSiteCount != 1 {
		t.Errorf("Expected SameSite to appear once, but appeared %d times", sameSiteCount)
	}
}

func TestEnvironmentDetection(t *testing.T) {
	tests := []struct {
		name     string
		ginMode  string
		envVar   string
		expected bool
	}{
		{"Development default", "", "", false},
		{"GIN_MODE release", "release", "", true},
		{"ENVIRONMENT production", "", "production", true},
		{"Both set", "release", "production", true},
		{"Case insensitive", "RELEASE", "PRODUCTION", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean environment
			os.Unsetenv("GIN_MODE")
			os.Unsetenv("ENVIRONMENT")

			// Set test values
			if tt.ginMode != "" {
				os.Setenv("GIN_MODE", tt.ginMode)
			}
			if tt.envVar != "" {
				os.Setenv("ENVIRONMENT", tt.envVar)
			}

			result := isProduction()
			if result != tt.expected {
				t.Errorf("isProduction() should return %v, got %v", tt.expected, result)
			}

			// Cleanup
			os.Unsetenv("GIN_MODE")
			os.Unsetenv("ENVIRONMENT")
		})
	}
}

func TestHTTPSDetection(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name     string
		setupReq func(*http.Request)
		expected bool
	}{
		{
			name:     "HTTP request",
			setupReq: func(req *http.Request) {},
			expected: false,
		},
		{
			name: "X-Forwarded-Proto HTTPS",
			setupReq: func(req *http.Request) {
				req.Header.Set("X-Forwarded-Proto", "https")
			},
			expected: true,
		},
		{
			name: "X-Forwarded-Ssl on",
			setupReq: func(req *http.Request) {
				req.Header.Set("X-Forwarded-Ssl", "on")
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			req := httptest.NewRequest("GET", "/", nil)
			tt.setupReq(req)
			c.Request = req

			result := isHTTPS(c)
			if result != tt.expected {
				t.Errorf("isHTTPS() should return %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestLoggingMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		method         string
		path           string
		expectedStatus int
		isSensitive    bool
	}{
		{
			name:           "Normal GET request",
			method:         "GET",
			path:           "/api/properties",
			expectedStatus: 200,
			isSensitive:    false,
		},
		{
			name:           "Sensitive auth request",
			method:         "POST",
			path:           "/v1/auth/login",
			expectedStatus: 200,
			isSensitive:    true,
		},
		{
			name:           "Admin operation",
			method:         "GET",
			path:           "/v1/admin/users",
			expectedStatus: 200,
			isSensitive:    true,
		},
		{
			name:           "Error request",
			method:         "GET",
			path:           "/nonexistent",
			expectedStatus: 404,
			isSensitive:    false,
		},
		{
			name:           "DELETE operation",
			method:         "DELETE",
			path:           "/api/property/123",
			expectedStatus: 200,
			isSensitive:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test router with Logging middleware
			r := gin.New()
			r.Use(Logging())

			// Add test routes
			r.Any("/*path", func(c *gin.Context) {
				if tt.expectedStatus == 404 {
					c.JSON(404, gin.H{"error": "not found"})
				} else {
					c.JSON(200, gin.H{"status": "ok"})
				}
			})

			// Create test request
			req := httptest.NewRequest(tt.method, tt.path, nil)
			req.Header.Set("User-Agent", "test-agent")

			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			// Check response status
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Test that isSensitiveOperation works correctly
			actualSensitive := isSensitiveOperation(tt.method, tt.path)
			if actualSensitive != tt.isSensitive {
				t.Errorf("Expected isSensitive=%v, got %v for %s %s",
					tt.isSensitive, actualSensitive, tt.method, tt.path)
			}
		})
	}
}

func TestIsSensitiveOperation(t *testing.T) {
	tests := []struct {
		method   string
		path     string
		expected bool
	}{
		{"GET", "/v1/admin/users", true},
		{"POST", "/v1/auth/login", true},
		{"PUT", "/v1/user/password", true},
		{"POST", "/v1/user/verify", true},
		{"DELETE", "/api/property/123", true},
		{"GET", "/api/delete/something", true},
		{"GET", "/api/properties", false},
		{"POST", "/api/lease", false},
		{"PUT", "/api/property/123", false},
	}

	for _, tt := range tests {
		t.Run(tt.method+" "+tt.path, func(t *testing.T) {
			result := isSensitiveOperation(tt.method, tt.path)
			if result != tt.expected {
				t.Errorf("isSensitiveOperation(%s, %s) = %v, expected %v",
					tt.method, tt.path, result, tt.expected)
			}
		})
	}
}
