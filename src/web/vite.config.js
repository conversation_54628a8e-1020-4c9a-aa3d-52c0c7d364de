import { defineConfig } from 'vite'
import { resolve } from 'path'
import viteMustachePlugin from './vite-mustache-plugin'
import viteCompression from 'vite-plugin-compression'
import { mockResponses } from './mock-server'
import { globSync } from 'glob'
import fs from 'fs'

// 根据环境设置基础URL
const BASE_URL = process.env.VITE_BASE_URL || ''

// 自动收集 pages 目录下所有 index.html 页面
const pageInputs = globSync(resolve(__dirname, 'pages/**/index.html')).reduce((inputs, file) => {
  // 匹配一级目录: pages/login/index.html -> login
  const singleLevelMatch = file.match(/pages\/([^/]+)\/index\.html$/)
  if (singleLevelMatch) {
    inputs[singleLevelMatch[1]] = file
    return inputs
  }

  // 匹配blog文章: pages/blog/article-slug/index.html -> blog-article-slug
  const blogMatch = file.match(/pages\/blog\/([^/]+)\/index\.html$/)
  if (blogMatch) {
    inputs[`blog-${blogMatch[1]}`] = file
    return inputs
  }

  return inputs
}, {})

// 自动收集 pages 目录下所有 main.js 文件
const pageScripts = globSync(resolve(__dirname, 'pages/**/main.js')).reduce((scripts, file) => {
  // 匹配一级目录: pages/login/main.js -> login-script
  const singleLevelMatch = file.match(/pages\/([^/]+)\/main\.js$/)
  if (singleLevelMatch) {
    scripts[`${singleLevelMatch[1]}-script`] = file
    return scripts
  }

  // 匹配blog文章: pages/blog/article-slug/main.js -> blog-article-slug-script
  const blogMatch = file.match(/pages\/blog\/([^/]+)\/main\.js$/)
  if (blogMatch) {
    scripts[`blog-${blogMatch[1]}-script`] = file
    return scripts
  }

  return scripts
}, {})

// 复制静态资源插件
function copyAssetsPlugin() {
  return {
    name: 'copy-assets',
    writeBundle() {
      // 确保dist/assets目录存在
      const distAssetsDir = resolve(__dirname, 'dist/assets')
      if (!fs.existsSync(distAssetsDir)) {
        fs.mkdirSync(distAssetsDir, { recursive: true })
      }
      
      // 复制assets目录下的所有文件
      const assetsDir = resolve(__dirname, 'assets')
      if (fs.existsSync(assetsDir)) {
        const copyDir = (src, dest) => {
          if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true })
          }
          
          const items = fs.readdirSync(src)
          items.forEach(item => {
            const srcPath = resolve(src, item)
            const destPath = resolve(dest, item)
            
            if (fs.statSync(srcPath).isDirectory()) {
              copyDir(srcPath, destPath)
            } else {
              fs.copyFileSync(srcPath, destPath)
            }
          })
        }
        
        copyDir(assetsDir, distAssetsDir)
      }
    }
  }
}

// 复制components目录下所有html文件到dist/components
function copyComponentsPlugin() {
  return {
    name: 'copy-components',
    writeBundle() {
      // 复制 src/web/components 到 dist/components
      const srcDir = resolve(__dirname, 'components');
      const distDir = resolve(__dirname, 'dist/components');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcDir)) {
        fs.readdirSync(srcDir).forEach(file => {
          if (file.endsWith('.html')) {
            fs.copyFileSync(
              resolve(srcDir, file),
              resolve(distDir, file)
            );
          }
        });
      }
      // 新增：复制 src/web/pages/pricing/components 到 dist/pages/pricing/components
      const pricingSrc = resolve(__dirname, 'pages/pricing/components');
      const pricingDist = resolve(__dirname, 'dist/pages/pricing/components');
      if (!fs.existsSync(pricingDist)) fs.mkdirSync(pricingDist, { recursive: true });
      if (fs.existsSync(pricingSrc)) {
        fs.readdirSync(pricingSrc).forEach(file => {
          if (file.endsWith('.html')) {
            fs.copyFileSync(
              resolve(pricingSrc, file),
              resolve(pricingDist, file)
            );
          }
        });
      }
    }
  }
}

// 复制pages目录下所有一级html文件到dist/pages
function copyPagesHtmlPlugin() {
  return {
    name: 'copy-pages-html',
    writeBundle() {
      const srcPages = resolve(__dirname, 'pages');
      const distPages = resolve(__dirname, 'dist/pages');
      if (!fs.existsSync(distPages)) fs.mkdirSync(distPages, { recursive: true });
      if (fs.existsSync(srcPages)) {
        fs.readdirSync(srcPages).forEach(file => {
          if (file.endsWith('.html')) {
            fs.copyFileSync(
              resolve(srcPages, file),
              resolve(distPages, file)
            );
          }
        });
      }
    }
  }
}

// 复制partials目录下所有mst文件到dist/partials
function copyPartialsPlugin() {
  return {
    name: 'copy-partials',
    writeBundle() {
      const srcPartials = resolve(__dirname, 'partials');
      const distPartials = resolve(__dirname, 'dist/partials');
      if (!fs.existsSync(distPartials)) fs.mkdirSync(distPartials, { recursive: true });
      if (fs.existsSync(srcPartials)) {
        fs.readdirSync(srcPartials).forEach(file => {
          if (file.endsWith('.mst')) {
            fs.copyFileSync(
              resolve(srcPartials, file),
              resolve(distPartials, file)
            );
          }
        });
      }
    }
  }
}

// 复制properties目录下的modal-create-property.html到dist/pages/properties
function copyPropertiesModalsPlugin() {
  return {
    name: 'copy-properties-modals',
    writeBundle() {
      const srcFile = resolve(__dirname, 'pages/properties/modal-create-property.html');
      const distDir = resolve(__dirname, 'dist/pages/properties');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'modal-create-property.html'));
      }
    }
  }
}

// 复制leases目录下的modal-create-lease.html和modal-add-lease.html到dist/pages/leases
function copyLeasesModalsPlugin() {
  return {
    name: 'copy-leases-modals',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/leases');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      // 复制 modal-create-lease.html
      const srcFile1 = resolve(__dirname, 'pages/leases/modal-create-lease.html');
      if (fs.existsSync(srcFile1)) {
        fs.copyFileSync(srcFile1, resolve(distDir, 'modal-create-lease.html'));
      }
      // 复制 modal-add-lease.html
      const srcFile2 = resolve(__dirname, 'pages/leases/modal-add-lease.html');
      if (fs.existsSync(srcFile2)) {
        fs.copyFileSync(srcFile2, resolve(distDir, 'modal-add-lease.html'));
      }
      // 复制 modal-lease-address.html
      const srcFile3 = resolve(__dirname, 'pages/leases/modal-lease-address.html');
      if (fs.existsSync(srcFile3)) {
        fs.copyFileSync(srcFile3, resolve(distDir, 'modal-lease-address.html'));
      }
      // 复制 modal-lease-info.html
      const srcFile4 = resolve(__dirname, 'pages/leases/modal-lease-info.html');
      if (fs.existsSync(srcFile4)) {
        fs.copyFileSync(srcFile4, resolve(distDir, 'modal-lease-info.html'));
      }
      // 复制 modal-lease-confirm.html
      const srcFile5 = resolve(__dirname, 'pages/leases/modal-lease-confirm.html');
      if (fs.existsSync(srcFile5)) {
        fs.copyFileSync(srcFile5, resolve(distDir, 'modal-lease-confirm.html'));
      }
    }
  }
}

// 复制property_detail目录下的modal-add-room.html到dist/pages/property_detail
function copyPropertyDetailModalsPlugin() {
  return {
    name: 'copy-property-detail-modals',
    writeBundle() {
      const srcFile = resolve(__dirname, 'pages/property_detail/modal-add-room.html');
      const distDir = resolve(__dirname, 'dist/pages/property_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'modal-add-room.html'));
      }
    }
  }
}

// 复制lease_detail目录下的modal-add-tenant.html和modal-add-payment.html到dist/pages/lease_detail
function copyLeaseDetailModalsPlugin() {
  return {
    name: 'copy-lease-detail-modals',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/lease_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const files = ['modal-add-tenant.html', 'modal-add-payment.html'];
      files.forEach(file => {
        const srcFile = resolve(__dirname, 'pages/lease_detail/' + file);
        if (fs.existsSync(srcFile)) {
          fs.copyFileSync(srcFile, resolve(distDir, file));
        }
      });
    }
  }
}

// 复制 admin/user_detail/index.html 到 dist/pages/admin/user_detail
function copyAdminUserDetailPlugin() {
  return {
    name: 'copy-admin-user-detail',
    writeBundle() {
      const srcFile = resolve(__dirname, 'pages/admin/user_detail/index.html');
      const distDir = resolve(__dirname, 'dist/pages/admin/user_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'index.html'));
      }
    }
  }
}

// 复制 admin/user_detail/main.js 和 main.css 到 dist/pages/admin/user_detail
function copyAdminUserDetailAssetsPlugin() {
  return {
    name: 'copy-admin-user-detail-assets',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/user_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      // 复制 main.js
      const jsFile = resolve(__dirname, 'pages/admin/user_detail/main.js');
      if (fs.existsSync(jsFile)) {
        fs.copyFileSync(jsFile, resolve(distDir, 'main.js'));
      }
      // 复制 main.css
      const cssFile = resolve(__dirname, 'styles/main.css');
      if (fs.existsSync(cssFile)) {
        fs.copyFileSync(cssFile, resolve(distDir, 'main.css'));
      }
    }
  }
}

// 复制 admin/lease_all/index.html 到 dist/pages/admin/lease_all
function copyAdminLeaseAllPlugin() {
  return {
    name: 'copy-admin-lease-all',
    writeBundle() {
      const srcFile = resolve(__dirname, 'pages/admin/lease_all/index.html');
      const distDir = resolve(__dirname, 'dist/pages/admin/lease_all');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'index.html'));
      }
    }
  }
}

// 复制 admin/payment_all/index.html 到 dist/pages/admin/payment_all
function copyAdminPaymentAllPlugin() {
  return {
    name: 'copy-admin-payment-all',
    writeBundle() {
      const srcFile = resolve(__dirname, 'pages/admin/payment_all/index.html');
      const distDir = resolve(__dirname, 'dist/pages/admin/payment_all');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'index.html'));
      }
    }
  }
}

// 复制 admin/property_all/index.html 到 dist/pages/admin/property_all
function copyAdminPropertyAllPlugin() {
  return {
    name: 'copy-admin-property-all',
    writeBundle() {
      const srcFile = resolve(__dirname, 'pages/admin/property_all/index.html');
      const distDir = resolve(__dirname, 'dist/pages/admin/property_all');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'index.html'));
      }
    }
  }
}

// 复制 admin/property_all/main.js 到 dist/pages/admin/property_all
function copyAdminPropertyAllJsPlugin() {
  return {
    name: 'copy-admin-property-all-js',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/property_all');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/property_all/main.js');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'main.js'));
      }
    }
  }
}

// 复制 admin/lease_all/main.js 到 dist/pages/admin/lease_all
function copyAdminLeaseAllJsPlugin() {
  return {
    name: 'copy-admin-lease-all-js',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/lease_all');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/lease_all/main.js');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'main.js'));
      }
    }
  }
}

// 复制 admin/payment_all/main.js 到 dist/pages/admin/payment_all
function copyAdminPaymentAllJsPlugin() {
  return {
    name: 'copy-admin-payment-all-js',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/payment_all');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/payment_all/main.js');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'main.js'));
      }
    }
  }
}

// 复制 admin/user_detail/admin_log.html 到 dist/pages/admin/user_detail
function copyAdminUserDetailLogHtmlPlugin() {
  return {
    name: 'copy-admin-user-detail-log-html',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/user_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/user_detail/admin_log.html');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'admin_log.html'));
      }
    }
  }
}

// 单独复制 styles/main.css 到 dist/styles/main.css
function copyMainCssPlugin() {
  return {
    name: 'copy-main-css',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/styles');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'styles/main.css');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'main.css'));
      }
    }
  }
}

// 复制 admin/property_detail/index.html 到 dist/pages/admin/property_detail
function copyAdminPropertyDetailPlugin() {
  return {
    name: 'copy-admin-property-detail',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/property_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/property_detail/index.html');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'index.html'));
      }
    }
  }
}

// 复制 admin/lease_detail/index.html 到 dist/pages/admin/lease_detail
function copyAdminLeaseDetailPlugin() {
  return {
    name: 'copy-admin-lease-detail',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/lease_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/lease_detail/index.html');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'index.html'));
      }
    }
  }
}

// 复制 admin/property_detail/main.js 到 dist/pages/admin/property_detail
function copyAdminPropertyDetailJsPlugin() {
  return {
    name: 'copy-admin-property-detail-js',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/property_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/property_detail/main.js');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'main.js'));
      }
    }
  }
}

// 复制 admin/lease_detail/main.js 到 dist/pages/admin/lease_detail
function copyAdminLeaseDetailJsPlugin() {
  return {
    name: 'copy-admin-lease-detail-js',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/pages/admin/lease_detail');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'pages/admin/lease_detail/main.js');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'main.js'));
      }
    }
  }
}

// 复制 styles/output.css 到 dist/styles/output.css
function copyOutputCssPlugin() {
  return {
    name: 'copy-output-css',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist/styles');
      if (!fs.existsSync(distDir)) fs.mkdirSync(distDir, { recursive: true });
      const srcFile = resolve(__dirname, 'styles/output.css');
      if (fs.existsSync(srcFile)) {
        fs.copyFileSync(srcFile, resolve(distDir, 'output.css'));
      }
    }
  }
}

export default defineConfig({
  base: BASE_URL, // 根据环境设置基础路径
  css: {
    postcss: './postcss.config.js'
  },
  // 开发服务器配置
  server: {
    open: false, // 禁止自动打开浏览器
    port: 3000,
    proxy: {
      '/v1': {
        target: 'http://localhost:8089/',
        changeOrigin: true,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            const url = req.url;
            if (mockResponses[url]) {
              mockResponses[url]().then(data => {
                // 根据请求路径判断响应类型
                const isHtmlRequest = url.startsWith('/api/html');
                const contentType = isHtmlRequest ? 'text/html' : 'application/json';
                const responseData = isHtmlRequest ? data : JSON.stringify(data);
                
                res.writeHead(200, {
                  'Content-Type': contentType,
                  'Content-Length': Buffer.byteLength(responseData)
                });
                res.end(responseData);
              });
            }
          });
        }
      }
    }
  },
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
    }
  },

  // 插件配置
  plugins: [
    copyAssetsPlugin(),
    copyComponentsPlugin(),
    copyPagesHtmlPlugin(),
    copyPartialsPlugin(),
    copyPropertiesModalsPlugin(),
    copyLeasesModalsPlugin(),
    copyPropertyDetailModalsPlugin(),
    copyLeaseDetailModalsPlugin(),
    copyAdminUserDetailPlugin(),
    copyAdminUserDetailAssetsPlugin(),
    copyAdminLeaseAllPlugin(),
    copyAdminPaymentAllPlugin(),
    copyAdminPropertyAllPlugin(),
    copyAdminPropertyAllJsPlugin(),
    copyAdminLeaseAllJsPlugin(),
    copyAdminPaymentAllJsPlugin(),
    copyAdminUserDetailLogHtmlPlugin(),
    copyMainCssPlugin(),
    copyAdminPropertyDetailPlugin(),
    copyAdminLeaseDetailPlugin(),
    copyAdminPropertyDetailJsPlugin(),
    copyAdminLeaseDetailJsPlugin(),
    copyOutputCssPlugin(),
    viteMustachePlugin({
      data: {
        //script: './main.js',
        script: '/main.js',
        baseUrl: BASE_URL
      },
      partialDirectory: resolve(__dirname, 'partials'),
    }),
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
      deleteOriginFile: false
    })
  ],

  // 构建配置
  build: {
    outDir: 'dist',
    // 启用 gzip 压缩大小报告
    reportCompressedSize: true,
    // CSS 代码分割
    cssCodeSplit: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        ...pageInputs,
        ...pageScripts
      },
      output: {
        // 分类输出文件
        entryFileNames: 'assets/js/[name]-[hash].js',
        chunkFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          // 对于assets目录下的文件，保持原始路径
          if (assetInfo.name && assetInfo.name.includes('assets/')) {
            return 'assets/[name]';
          }
          // 其他文件按扩展名分类
          return 'assets/[ext]/[name]-[hash].[ext]';
        }
      }
    },
    // 设置块大小警告的限制
    chunkSizeWarningLimit: 1000,
    // 确保静态资源被复制
    assetsInlineLimit: 0
  }
}) 