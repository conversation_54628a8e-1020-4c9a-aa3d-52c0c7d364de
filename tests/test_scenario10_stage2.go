package main

import (
	"fmt"
	"time"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID                    string
	Status                string
	AutoPay               bool
	StartDate             string
	EndDate               string
	RentDueDay            int
	RentAmount            float64
	AdditionalMonthlyFees float64
	OwingBalance          float64
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
	Date    string
}

// 模拟 createAutoPayment 函数
func createAutoPayment(lease TestLease, date time.Time) (TestPayment, error) {
	payment := TestPayment{
		ID:      fmt.Sprintf("payment-%s-%s", lease.ID, date.Format("2006-01")),
		LeaseID: lease.ID,
		Amount:  lease.RentAmount + lease.AdditionalMonthlyFees,
		Date:    date.Format("2006-01-02"),
	}
	return payment, nil
}

// 模拟 updateBalanceOnDueDay 函数
func updateBalanceOnDueDay(lease *TestLease) error {
	// 计算月租金
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 增加余额（因为又欠了一个月的租金）
	newBalance := lease.OwingBalance + monthlyRent

	// 允许负数余额，表示多付款

	lease.OwingBalance = newBalance
	return nil
}

// 模拟 Scenario 10 阶段2的差异化处理
func processLeaseOnDueDay(lease *TestLease, today time.Time) (interface{}, string, error) {
	if lease.AutoPay {
		// AutoPay=true，创建支付记录
		payment, err := createAutoPayment(*lease, today)
		if err != nil {
			return nil, "auto_payment_failed", err
		}

		// 更新余额（减去支付金额）
		lease.OwingBalance -= payment.Amount
		// 允许负数余额，表示多付款

		return payment, "auto_payment_created", nil
	} else {
		// AutoPay=false，只更新余额
		err := updateBalanceOnDueDay(lease)
		if err != nil {
			return nil, "balance_update_failed", err
		}

		return nil, "balance_updated", nil
	}
}

// TestScenario10Stage2 测试阶段2：差异化处理逻辑
func TestScenario10Stage2() {
	fmt.Println("=== 测试 Scenario 10 阶段2：差异化处理逻辑 ===")

	today := time.Now()
	currentDay := today.Day()
	fmt.Printf("当前日期: %s (第%d天)\n\n", today.Format("2006-01-02"), currentDay)

	// 创建测试租约数据
	testLeases := []TestLease{
		{
			ID:                    "lease-autopay-true",
			Status:                "active",
			AutoPay:               true,
			StartDate:             "2025-01-01",
			EndDate:               "",
			RentDueDay:            currentDay, // 今天到期
			RentAmount:            1000.0,
			AdditionalMonthlyFees: 200.0,
			OwingBalance:          1200.0,
		},
		{
			ID:                    "lease-autopay-false",
			Status:                "active",
			AutoPay:               false,
			StartDate:             "2025-01-01",
			EndDate:               "",
			RentDueDay:            currentDay, // 今天到期
			RentAmount:            800.0,
			AdditionalMonthlyFees: 150.0,
			OwingBalance:          950.0,
		},
		{
			ID:                    "lease-not-due",
			Status:                "active",
			AutoPay:               true,
			StartDate:             "2025-01-01",
			EndDate:               "",
			RentDueDay:            (currentDay % 28) + 1, // 不是今天到期
			RentAmount:            1200.0,
			AdditionalMonthlyFees: 300.0,
			OwingBalance:          1500.0,
		},
	}

	fmt.Printf("=== 测试差异化处理 ===\n")

	for i, lease := range testLeases {
		fmt.Printf("测试租约 %d: %s\n", i+1, lease.ID)
		fmt.Printf("  AutoPay: %t\n", lease.AutoPay)
		fmt.Printf("  到期日: %d号 (今天: %d号)\n", lease.RentDueDay, currentDay)
		fmt.Printf("  月租金: $%.2f + $%.2f = $%.2f\n",
			lease.RentAmount, lease.AdditionalMonthlyFees,
			lease.RentAmount+lease.AdditionalMonthlyFees)
		fmt.Printf("  处理前余额: $%.2f\n", lease.OwingBalance)

		// 检查是否是到期日
		if lease.RentDueDay == currentDay {
			fmt.Printf("  🎯 今天是到期日，开始处理...\n")

			// 保存原始余额用于比较
			originalBalance := lease.OwingBalance

			// 处理租约
			result, action, err := processLeaseOnDueDay(&lease, today)

			if err != nil {
				fmt.Printf("  ❌ 处理失败: %v\n", err)
			} else {
				fmt.Printf("  ✅ 处理成功: %s\n", action)
				fmt.Printf("  处理后余额: $%.2f\n", lease.OwingBalance)

				// 验证处理结果
				switch action {
				case "auto_payment_created":
					if payment, ok := result.(TestPayment); ok {
						fmt.Printf("  💳 创建支付记录: $%.2f\n", payment.Amount)

						// 验证余额变化
						expectedBalance := originalBalance // 支付后余额应该保持不变或减少
						if lease.OwingBalance <= expectedBalance {
							fmt.Printf("  ✅ 余额变化正确\n")
						} else {
							fmt.Printf("  ❌ 余额变化异常\n")
						}
					}

				case "balance_updated":
					fmt.Printf("  📊 仅更新余额\n")

					// 验证余额增加
					monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees
					expectedBalance := originalBalance + monthlyRent
					if lease.OwingBalance == expectedBalance {
						fmt.Printf("  ✅ 余额增加正确 (+$%.2f)\n", monthlyRent)
					} else {
						fmt.Printf("  ❌ 余额增加错误 (期望: $%.2f, 实际: $%.2f)\n",
							expectedBalance, lease.OwingBalance)
					}
				}
			}
		} else {
			fmt.Printf("  ⏳ 今天不是到期日，跳过处理\n")
		}

		fmt.Println()
	}

	// 测试边界情况
	fmt.Printf("=== 边界情况测试 ===\n")

	// 测试零余额情况
	fmt.Printf("1. 零余额 + AutoPay=false:\n")
	zeroBalanceLease := TestLease{
		ID:                    "lease-zero-balance",
		Status:                "active",
		AutoPay:               false,
		RentDueDay:            currentDay,
		RentAmount:            500.0,
		AdditionalMonthlyFees: 100.0,
		OwingBalance:          0.0,
	}

	fmt.Printf("  处理前余额: $%.2f\n", zeroBalanceLease.OwingBalance)
	_, _, err := processLeaseOnDueDay(&zeroBalanceLease, today)
	if err == nil {
		fmt.Printf("  处理后余额: $%.2f\n", zeroBalanceLease.OwingBalance)
		if zeroBalanceLease.OwingBalance == 600.0 {
			fmt.Printf("  ✅ 零余额处理正确\n")
		} else {
			fmt.Printf("  ❌ 零余额处理错误\n")
		}
	}

	// 测试高余额 + AutoPay=true
	fmt.Printf("\n2. 高余额 + AutoPay=true:\n")
	highBalanceLease := TestLease{
		ID:                    "lease-high-balance",
		Status:                "active",
		AutoPay:               true,
		RentDueDay:            currentDay,
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		OwingBalance:          3600.0, // 3个月的欠款
	}

	fmt.Printf("  处理前余额: $%.2f\n", highBalanceLease.OwingBalance)
	result, _, err := processLeaseOnDueDay(&highBalanceLease, today)
	if err == nil {
		fmt.Printf("  处理后余额: $%.2f\n", highBalanceLease.OwingBalance)
		if payment, ok := result.(TestPayment); ok {
			fmt.Printf("  创建支付: $%.2f\n", payment.Amount)
			expectedBalance := 3600.0 - 1200.0 // 原余额 - 支付金额
			if highBalanceLease.OwingBalance == expectedBalance {
				fmt.Printf("  ✅ 高余额处理正确\n")
			} else {
				fmt.Printf("  ❌ 高余额处理错误\n")
			}
		}
	}

	// 统计测试结果
	fmt.Printf("\n=== 测试总结 ===\n")
	fmt.Printf("差异化处理逻辑:\n")
	fmt.Printf("  AutoPay=true  → 创建支付记录 + 减少余额\n")
	fmt.Printf("  AutoPay=false → 仅增加余额\n")
	fmt.Printf("触发条件: 租约到期日 = 当前日期\n")
	fmt.Printf("余额保护: 允许负数余额（表示多付款）\n")

	fmt.Printf("\n=== 阶段2测试完成 ===\n")
}

func main() {
	TestScenario10Stage2()
}
