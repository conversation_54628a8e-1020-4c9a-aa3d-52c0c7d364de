package main

import (
	"fmt"
	"time"
)

// 简化的租约结构体用于测试
type TestLease struct {
	ID                    string
	RentAmount            float64
	AdditionalMonthlyFees float64
	StartDate             string
	RentDueDay            int
	OwingBalance          float64
	Status                string
}

// 简化的支付结构体用于测试
type TestPayment struct {
	ID      string
	LeaseID string
	Amount  float64
	Date    string
}

// 模拟基于到期日的月数计算
func calculateMonthsBasedOnDueDay(startDate, currentDate time.Time, rentDueDay int) int {
	if startDate.After(currentDate) {
		return 0
	}

	if rentDueDay < 1 || rentDueDay > 31 {
		rentDueDay = 1
	}

	months := 0
	checkDate := time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, startDate.Location())
	currentMonth := time.Date(currentDate.Year(), currentDate.Month(), 1, 0, 0, 0, 0, currentDate.Location())

	for !checkDate.After(currentMonth) {
		dueDate := time.Date(checkDate.Year(), checkDate.Month(), rentDueDay, 0, 0, 0, 0, checkDate.Location())
		lastDayOfMonth := time.Date(checkDate.Year(), checkDate.Month()+1, 0, 0, 0, 0, 0, checkDate.Location()).Day()
		if rentDueDay > lastDayOfMonth {
			dueDate = time.Date(checkDate.Year(), checkDate.Month(), lastDayOfMonth, 0, 0, 0, 0, checkDate.Location())
		}

		if currentDate.After(dueDate) || currentDate.Equal(dueDate) {
			months++
		}

		checkDate = checkDate.AddDate(0, 1, 0)
	}

	return months
}

// 模拟使用指定租金计算初始余额
func calculateInitialOwingBalanceWithRent(lease *TestLease, monthlyRent float64) float64 {
	startDate, err := time.Parse("2006-01-02", lease.StartDate)
	if err != nil {
		return monthlyRent
	}

	now := time.Now()
	if startDate.After(now) {
		return 0.0
	}

	months := calculateMonthsBasedOnDueDay(startDate, now, lease.RentDueDay)
	if months < 1 {
		months = 1
	}

	return float64(months) * monthlyRent
}

// 模拟 Scenario 6 的租金变更处理
func simulateRentAmountChange(originalLease, newLease *TestLease, payments []TestPayment) error {
	// 检查租金是否发生变更
	oldMonthlyRent := originalLease.RentAmount + originalLease.AdditionalMonthlyFees
	newMonthlyRent := newLease.RentAmount + newLease.AdditionalMonthlyFees

	if oldMonthlyRent == newMonthlyRent {
		return nil // 租金没有变更，无需处理
	}

	// Scenario 6: 重新计算租约余额以反映租金变更
	// 1. 计算基于新租金的初始余额
	newInitialBalance := calculateInitialOwingBalanceWithRent(newLease, newMonthlyRent)

	// 2. 减去所有支付
	totalPayments := 0.0
	for _, payment := range payments {
		totalPayments += payment.Amount
	}

	// 3. 计算新余额
	newBalance := newInitialBalance - totalPayments
	if newBalance < 0 {
		newBalance = 0
	}

	// 4. 更新租约余额
	newLease.OwingBalance = newBalance

	return nil
}

// TestScenario6 测试租金变更的时间限制处理
func TestScenario6() {
	fmt.Println("=== 测试 Scenario 6: 租金变更的时间限制处理 ===")

	now := time.Now()
	fmt.Printf("当前时间: %s\n\n", now.Format("2006-01-02"))

	// 测试案例
	testCases := []struct {
		name               string
		startDate          string
		rentDueDay         int
		oldRent            float64
		oldFees            float64
		newRent            float64
		newFees            float64
		payments           []TestPayment
		expectedOldBalance float64
		expectedNewBalance float64
		description        string
	}{
		{
			name:       "租金增加",
			startDate:  "2025-05-01",
			rentDueDay: 5,
			oldRent:    1000.0,
			oldFees:    200.0,
			newRent:    1200.0,
			newFees:    200.0,
			payments: []TestPayment{
				{Amount: 600.0, Date: "2025-06-01"},
			},
			expectedOldBalance: 1800.0, // 2个月 * $1200 - $600 = 1800
			expectedNewBalance: 2200.0, // 2个月 * $1400 - $600 = 2200
			description:        "租金从$1000增加到$1200，余额应该增加",
		},
		{
			name:       "租金减少",
			startDate:  "2025-04-01",
			rentDueDay: 1,
			oldRent:    1500.0,
			oldFees:    300.0,
			newRent:    1200.0,
			newFees:    200.0,
			payments: []TestPayment{
				{Amount: 1800.0, Date: "2025-05-01"},
				{Amount: 1400.0, Date: "2025-06-01"},
			},
			expectedOldBalance: 4000.0, // 4个月 * $1800 - $3200 = 4000
			expectedNewBalance: 2400.0, // 4个月 * $1400 - $3200 = 2400
			description:        "租金从$1500减少到$1200，余额应该减少",
		},
		{
			name:       "只修改附加费用",
			startDate:  "2025-06-01",
			rentDueDay: 15,
			oldRent:    1000.0,
			oldFees:    100.0,
			newRent:    1000.0,
			newFees:    250.0,
			payments: []TestPayment{
				{Amount: 500.0, Date: "2025-06-20"},
			},
			expectedOldBalance: 600.0, // 1个月 * $1100 - $500
			expectedNewBalance: 750.0, // 1个月 * $1250 - $500
			description:        "只修改附加费用，从$100增加到$250",
		},
		{
			name:       "租金不变",
			startDate:  "2025-05-01",
			rentDueDay: 10,
			oldRent:    1000.0,
			oldFees:    200.0,
			newRent:    1000.0,
			newFees:    200.0,
			payments: []TestPayment{
				{Amount: 1200.0, Date: "2025-06-01"},
			},
			expectedOldBalance: 1200.0, // 3个月 * $1200 - $1200
			expectedNewBalance: 1200.0, // 租金不变，余额不变
			description:        "租金和费用都不变，余额应该保持不变",
		},
		{
			name:       "大幅租金变更",
			startDate:  "2025-03-01",
			rentDueDay: 1,
			oldRent:    800.0,
			oldFees:    100.0,
			newRent:    1500.0,
			newFees:    400.0,
			payments: []TestPayment{
				{Amount: 900.0, Date: "2025-04-01"},
				{Amount: 900.0, Date: "2025-05-01"},
			},
			expectedOldBalance: 1800.0, // 5个月 * $900 - $1800
			expectedNewBalance: 7700.0, // 5个月 * $1900 - $1800
			description:        "大幅租金变更，从$900/月增加到$1900/月",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("  %s\n", tc.description)

		// 创建原始租约
		originalLease := &TestLease{
			ID:                    "lease-1",
			RentAmount:            tc.oldRent,
			AdditionalMonthlyFees: tc.oldFees,
			StartDate:             tc.startDate,
			RentDueDay:            tc.rentDueDay,
			Status:                "active",
		}

		// 计算原始余额
		oldMonthlyRent := originalLease.RentAmount + originalLease.AdditionalMonthlyFees
		originalLease.OwingBalance = calculateInitialOwingBalanceWithRent(originalLease, oldMonthlyRent)
		for _, payment := range tc.payments {
			originalLease.OwingBalance -= payment.Amount
		}
		if originalLease.OwingBalance < 0 {
			originalLease.OwingBalance = 0
		}

		// 创建新租约
		newLease := &TestLease{
			ID:                    "lease-1",
			RentAmount:            tc.newRent,
			AdditionalMonthlyFees: tc.newFees,
			StartDate:             tc.startDate,
			RentDueDay:            tc.rentDueDay,
			Status:                "active",
			OwingBalance:          originalLease.OwingBalance, // 初始为原余额
		}

		fmt.Printf("  开始日期: %s, 到期日: %d号\n", tc.startDate, tc.rentDueDay)
		fmt.Printf("  租金变更: $%.2f+$%.2f → $%.2f+$%.2f (月租: $%.2f → $%.2f)\n",
			tc.oldRent, tc.oldFees, tc.newRent, tc.newFees,
			oldMonthlyRent, tc.newRent+tc.newFees)
		fmt.Printf("  支付记录: %d笔，总计: $%.2f\n", len(tc.payments), func() float64 {
			total := 0.0
			for _, p := range tc.payments {
				total += p.Amount
			}
			return total
		}())
		fmt.Printf("  变更前余额: $%.2f\n", originalLease.OwingBalance)

		// 应用 Scenario 6 逻辑
		err := simulateRentAmountChange(originalLease, newLease, tc.payments)
		if err != nil {
			fmt.Printf("  ❌ 处理失败: %v\n", err)
		} else {
			fmt.Printf("  变更后余额: $%.2f\n", newLease.OwingBalance)
			fmt.Printf("  预期余额: $%.2f\n", tc.expectedNewBalance)

			if newLease.OwingBalance == tc.expectedNewBalance {
				fmt.Printf("  ✅ 通过\n")
			} else {
				fmt.Printf("  ❌ 失败\n")
			}
		}
		fmt.Println()
	}

	// 边界情况测试
	fmt.Printf("=== 边界情况测试 ===\n")

	// 测试零支付情况
	fmt.Printf("1. 零支付情况:\n")
	zeroPaymentLease := &TestLease{
		ID:                    "lease-zero",
		RentAmount:            1000.0,
		AdditionalMonthlyFees: 200.0,
		StartDate:             "2025-05-01",
		RentDueDay:            1,
		Status:                "active",
		OwingBalance:          3600.0, // 3个月 * $1200
	}

	newZeroLease := *zeroPaymentLease
	newZeroLease.RentAmount = 1500.0
	newZeroLease.AdditionalMonthlyFees = 300.0

	fmt.Printf("  租金变更: $1200/月 → $1800/月，无支付记录\n")
	fmt.Printf("  变更前余额: $%.2f\n", zeroPaymentLease.OwingBalance)

	err := simulateRentAmountChange(zeroPaymentLease, &newZeroLease, []TestPayment{})
	if err == nil {
		fmt.Printf("  变更后余额: $%.2f\n", newZeroLease.OwingBalance)
		// 应该是 3个月 * $1800 = $5400
		if newZeroLease.OwingBalance == 5400.0 {
			fmt.Printf("  ✅ 零支付情况处理正确\n")
		} else {
			fmt.Printf("  ❌ 零支付情况处理错误\n")
		}
	}

	fmt.Printf("\n=== Scenario 6 测试完成 ===\n")
}

func main() {
	TestScenario6()
}
