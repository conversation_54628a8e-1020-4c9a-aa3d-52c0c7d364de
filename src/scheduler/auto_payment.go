package scheduler

import (
	"context"
	"fmt"
	"rent_report/entities"
	"rent_report/utils"
	"time"

	"github.com/real-rm/goconfig"
	"github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// AutoPaymentConfig 自动支付配置
type AutoPaymentConfig struct {
	Enabled       bool
	ExecutionHour int // 执行时间（小时，0-23）
	MaxRetries    int // 最大重试次数
}

// getAutoPaymentConfig 获取自动支付配置
func getAutoPaymentConfig() AutoPaymentConfig {
	enabled := true
	if val, ok := goconfig.Config("autoPayment.enabled").(bool); ok {
		enabled = val
	}

	executionHour := 1
	if val, ok := goconfig.Config("autoPayment.executionHour").(int); ok {
		executionHour = val
	}

	maxRetries := 3
	if val, ok := goconfig.Config("autoPayment.maxRetries").(int); ok {
		maxRetries = val
	}

	return AutoPaymentConfig{
		Enabled:       enabled,
		ExecutionHour: executionHour,
		MaxRetries:    maxRetries,
	}
}

// StartAutoPaymentScheduler 启动自动支付调度器
func StartAutoPaymentScheduler() {
	config := getAutoPaymentConfig()

	if !config.Enabled {
		golog.Info("Auto payment scheduler is disabled")
		return
	}

	golog.Info("Starting auto payment scheduler...",
		"executionHour", config.ExecutionHour,
		"maxRetries", config.MaxRetries)

	// 启动一个goroutine来运行定时任务
	go func() {
		// 每天执行检查
		ticker := time.NewTicker(24 * time.Hour)
		defer ticker.Stop()

		// 计算到下一个执行时间
		now := time.Now()
		nextExecution := time.Date(now.Year(), now.Month(), now.Day()+1, config.ExecutionHour, 0, 0, 0, now.Location())
		if now.Hour() < config.ExecutionHour {
			nextExecution = time.Date(now.Year(), now.Month(), now.Day(), config.ExecutionHour, 0, 0, 0, now.Location())
		}

		golog.Info("Next auto payment execution scheduled", "time", nextExecution.Format("2006-01-02 15:04:05"))

		// 等待到下一个执行时间
		time.Sleep(time.Until(nextExecution))

		// 立即执行一次
		processAutoPaymentsWithRetry(config.MaxRetries)

		// 然后每24小时执行一次
		for range ticker.C {
			processAutoPaymentsWithRetry(config.MaxRetries)
		}
	}()
}

// processAutoPaymentsWithRetry 带重试机制的自动支付处理
func processAutoPaymentsWithRetry(maxRetries int) {
	for attempt := 1; attempt <= maxRetries; attempt++ {
		golog.Info("Processing auto payments", "attempt", attempt, "maxRetries", maxRetries)

		err := processAutoPayments()
		if err == nil {
			golog.Info("Auto payments processed successfully")
			return
		}

		golog.Error("Auto payment processing failed",
			"attempt", attempt,
			"maxRetries", maxRetries,
			"error", err)

		if attempt < maxRetries {
			// 等待一段时间后重试
			waitTime := time.Duration(attempt) * 5 * time.Minute
			golog.Info("Waiting before retry", "waitTime", waitTime)
			time.Sleep(waitTime)
		}
	}

	golog.Error("Auto payment processing failed after all retries", "maxRetries", maxRetries)
}

// processAutoPayments 处理自动支付
func processAutoPayments() error {
	golog.Info("Processing auto payments...")

	ctx := context.Background()
	today := time.Now()
	currentDay := today.Day()

	// 查找所有需要处理的活跃租约
	leases, err := getActiveLeasesForProcessing(ctx)
	if err != nil {
		golog.Error("Failed to get active auto pay leases", "error", err)
		return fmt.Errorf("failed to get active auto pay leases: %v", err)
	}

	golog.Info(fmt.Sprintf("Found %d active leases for processing (status=active, within valid date range)", len(leases)))

	var errors []string
	successCount := 0

	for _, lease := range leases {
		// 检查今天是否是该租约的租金到期日
		if lease.RentDueDay == currentDay {
			if lease.AutoPay {
				// Scenario 10: AutoPay=true，创建支付记录（现有逻辑）
				err := createAutoPayment(ctx, lease, today)
				if err != nil {
					errorMsg := fmt.Sprintf("Failed to create auto payment for lease %s: %v", lease.ID, err)
					golog.Error(errorMsg)
					errors = append(errors, errorMsg)
				} else {
					golog.Info("Auto payment created successfully",
						"leaseId", lease.ID,
						"amount", lease.RentAmount)
					successCount++
				}
			} else {
				// Scenario 10: AutoPay=false，只更新余额（新逻辑）
				err := updateBalanceOnDueDay(ctx, lease)
				if err != nil {
					errorMsg := fmt.Sprintf("Failed to update balance on due day for lease %s: %v", lease.ID, err)
					golog.Error(errorMsg)
					errors = append(errors, errorMsg)
				} else {
					golog.Info("Balance updated on due day",
						"leaseId", lease.ID,
						"monthlyRent", lease.RentAmount+lease.AdditionalMonthlyFees)
					successCount++
				}
			}
		}
	}

	golog.Info("Auto payment processing completed",
		"successCount", successCount,
		"errorCount", len(errors))

	if len(errors) > 0 {
		return fmt.Errorf("auto payment processing completed with %d errors: %v", len(errors), errors)
	}

	return nil
}

// getActiveLeasesForProcessing 获取所有需要处理的活跃租约（包括自动支付和余额更新）
func getActiveLeasesForProcessing(ctx context.Context) ([]entities.Lease, error) {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	now := time.Now()
	today := now.Format("2006-01-02")

	// 查询条件：状态为active且在有效期内（包括自动支付和手动支付的租约）
	filter := bson.M{
		"status": "active",
		// 检查开始日期：租约已开始
		"startDt": bson.M{"$lte": today},
		// 检查结束日期：无结束日期或结束日期在今天或未来
		"$or": []bson.M{
			{"endDt": ""},                    // 无结束日期
			{"endDt": bson.M{"$gte": today}}, // 结束日期在今天或未来
		},
	}

	result, err := leaseColl.FindToArray(ctx, filter, gomongo.QueryOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to query leases: %v", err)
	}

	var leases []entities.Lease
	for _, item := range result {
		var lease entities.Lease
		data, err := bson.Marshal(item)
		if err != nil {
			golog.Error("Failed to marshal lease data", "error", err)
			continue
		}

		if err := bson.Unmarshal(data, &lease); err != nil {
			golog.Error("Failed to unmarshal lease data", "error", err)
			continue
		}

		leases = append(leases, lease)
	}

	return leases, nil
}

// isLeaseActiveOnDate 检查租约在指定日期是否有效
func isLeaseActiveOnDate(lease entities.Lease, date time.Time) bool {
	dateStr := date.Format("2006-01-02")

	// 检查开始日期：租约必须已开始
	if lease.StartDate != "" && lease.StartDate > dateStr {
		return false
	}

	// 检查结束日期：如果有结束日期，必须在结束日期之前或当天
	if lease.EndDate != "" && lease.EndDate < dateStr {
		return false
	}

	return true
}

// createAutoPayment 为指定租约创建自动支付记录
func createAutoPayment(ctx context.Context, lease entities.Lease, paymentDate time.Time) error {
	// 双重检查：确保租约在支付日期时仍然有效
	if !isLeaseActiveOnDate(lease, paymentDate) {
		golog.Info("Lease is not active on payment date, skipping auto payment",
			"leaseId", lease.ID,
			"paymentDate", paymentDate.Format("2006-01-02"),
			"startDate", lease.StartDate,
			"endDate", lease.EndDate)
		return nil
	}

	// 检查当月是否已经存在支付记录
	exists, err := checkPaymentExists(ctx, lease.ID, paymentDate)
	if err != nil {
		return fmt.Errorf("failed to check existing payment: %v", err)
	}

	if exists {
		golog.Info("Payment already exists for this month",
			"leaseId", lease.ID,
			"month", paymentDate.Format("2006-01"))
		return nil
	}

	// 创建支付记录
	payment := entities.TenantPayment{
		ID:               utils.GenerateNanoID(),
		LeaseID:          lease.ID,
		Amount:           lease.RentAmount,
		Date:             paymentDate,
		Notes:            "Auto payment",
		RemainingBalance: 0,
		Status:           "", // 根据你的需求，状态为空
		UserID:           lease.UserID,
	}

	err = payment.CreateAutoPayment(ctx)
	if err != nil {
		return fmt.Errorf("failed to create payment: %v", err)
	}

	return nil
}

// checkPaymentExists 检查指定租约在指定月份是否已存在支付记录
func checkPaymentExists(ctx context.Context, leaseID string, paymentDate time.Time) (bool, error) {
	paymentColl := gomongo.Coll("rr", "tenant_payments")
	if paymentColl == nil {
		return false, fmt.Errorf("tenant_payments collection not initialized")
	}

	// 计算当月的开始和结束时间
	year, month, _ := paymentDate.Date()
	monthStart := time.Date(year, month, 1, 0, 0, 0, 0, paymentDate.Location())
	monthEnd := monthStart.AddDate(0, 1, 0).Add(-time.Nanosecond)

	filter := bson.M{
		"leaseId": leaseID,
		"dt": bson.M{
			"$gte": monthStart,
			"$lte": monthEnd,
		},
	}

	count, err := paymentColl.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("failed to count payments: %v", err)
	}

	return count > 0, nil
}

// ProcessAutoPaymentsManually 手动触发自动支付处理（用于测试）
func ProcessAutoPaymentsManually() error {
	golog.Info("Manually processing auto payments...")
	return processAutoPayments()
}

// ProcessAutoPaymentsForTesting 测试用函数，忽略租金到期日检查但仍检查租约有效期
func ProcessAutoPaymentsForTesting() error {
	golog.Info("Processing auto payments for testing (ignoring rent due day check)...")

	ctx := context.Background()
	today := time.Now()

	// 查找所有需要处理的活跃租约（仍会检查租约有效期）
	leases, err := getActiveLeasesForProcessing(ctx)
	if err != nil {
		golog.Error("Failed to get active auto pay leases", "error", err)
		return fmt.Errorf("failed to get active auto pay leases: %v", err)
	}

	golog.Info(fmt.Sprintf("Found %d active auto pay leases for testing", len(leases)))

	var errors []string
	successCount := 0

	for _, lease := range leases {
		// 测试模式：根据 autoPay 设置进行处理（忽略租金到期日检查）
		if lease.AutoPay {
			err := createAutoPayment(ctx, lease, today)
			if err != nil {
				errorMsg := fmt.Sprintf("Failed to create auto payment for lease %s: %v", lease.ID, err)
				golog.Error(errorMsg)
				errors = append(errors, errorMsg)
			} else {
				golog.Info("Auto payment created successfully (testing)",
					"leaseId", lease.ID,
					"amount", lease.RentAmount)
				successCount++
			}
		} else {
			err := updateBalanceOnDueDay(ctx, lease)
			if err != nil {
				errorMsg := fmt.Sprintf("Failed to update balance for lease %s: %v", lease.ID, err)
				golog.Error(errorMsg)
				errors = append(errors, errorMsg)
			} else {
				golog.Info("Balance updated successfully (testing)",
					"leaseId", lease.ID,
					"monthlyRent", lease.RentAmount+lease.AdditionalMonthlyFees)
				successCount++
			}
		}
	}

	golog.Info("Auto payment testing completed",
		"successCount", successCount,
		"errorCount", len(errors))

	if len(errors) > 0 {
		return fmt.Errorf("auto payment testing completed with %d errors: %v", len(errors), errors)
	}

	return nil
}

// GetAutoPaymentStats 获取自动支付统计信息
func GetAutoPaymentStats(ctx context.Context) (map[string]interface{}, error) {
	now := time.Now()

	// 获取所有启用自动支付的租约（不考虑有效期）
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return nil, fmt.Errorf("leases collection not initialized")
	}

	allAutoPayFilter := bson.M{
		"status":  "active",
		"autoPay": true,
	}

	allAutoPayResult, err := leaseColl.FindToArray(ctx, allAutoPayFilter, gomongo.QueryOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to query all auto pay leases: %v", err)
	}

	// 获取有效期内的租约
	validLeases, err := getActiveLeasesForProcessing(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get valid auto pay leases: %v", err)
	}

	// 统计今天需要处理的租约
	todayDueCount := 0
	currentDay := now.Day()
	for _, lease := range validLeases {
		if lease.RentDueDay == currentDay {
			todayDueCount++
		}
	}

	stats := map[string]interface{}{
		"currentDay":         currentDay,
		"totalAutoPayLeases": len(allAutoPayResult),
		"validAutoPayLeases": len(validLeases),
		"todayDueCount":      todayDueCount,
		"lastProcessTime":    now.Format("2006-01-02 15:04:05"),
		"excludedLeases":     len(allAutoPayResult) - len(validLeases),
	}

	return stats, nil
}

// updateBalanceOnDueDay 在到期日更新余额（不创建支付记录）- Scenario 10
func updateBalanceOnDueDay(ctx context.Context, lease entities.Lease) error {
	// 检查当月是否已经处理过（避免重复处理）
	exists, err := checkBalanceUpdateExists(ctx, lease.ID, time.Now())
	if err != nil {
		return fmt.Errorf("failed to check existing balance update: %v", err)
	}

	if exists {
		golog.Info("Balance already updated for this month",
			"leaseId", lease.ID,
			"month", time.Now().Format("2006-01"))
		return nil
	}

	// 计算月租金（租金 + 附加费用）
	monthlyRent := lease.RentAmount + lease.AdditionalMonthlyFees

	// 创建资源范围（复用现有逻辑）
	scope := &entities.ResourceScope{
		OrganizationID: lease.OrganizationID,
		UserID:         lease.UserID,
	}

	// 增加余额（因为又欠了一个月的租金）
	err = updateLeaseBalanceForDueDay(ctx, lease.ID, monthlyRent, scope)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	// 更新租约的最后处理日期（复用 lastPmtDt 字段来避免重复处理）
	err = updateLastProcessDate(ctx, lease.ID, time.Now(), scope)
	if err != nil {
		golog.Warn("Failed to update last process date", "error", err)
		// 不返回错误，因为余额已经更新成功
	}

	return nil
}

// checkBalanceUpdateExists 检查当月是否已经处理过余额更新
func checkBalanceUpdateExists(ctx context.Context, leaseID string, date time.Time) (bool, error) {
	// 检查当月是否已有支付记录
	paymentExists, err := checkPaymentExists(ctx, leaseID, date)
	if err != nil {
		return false, err
	}

	if paymentExists {
		return true, nil // 有支付记录，说明已经处理过了
	}

	// 检查是否已经更新过余额（通过 lastPmtDt 字段）
	balanceUpdateExists, err := checkLastProcessDate(ctx, leaseID, date)
	if err != nil {
		return false, err
	}

	return balanceUpdateExists, nil
}

// checkLastProcessDate 检查是否在指定日期已经处理过
func checkLastProcessDate(ctx context.Context, leaseID string, date time.Time) (bool, error) {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return false, fmt.Errorf("leases collection not initialized")
	}

	filter := bson.M{"_id": leaseID}

	var lease entities.Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&lease)
	if err != nil {
		return false, fmt.Errorf("failed to find lease: %v", err)
	}

	// 检查 lastPmtDt 是否是今天
	if lease.LastPaymentDate != "" {
		lastProcessDate, err := time.Parse("2006-01-02", lease.LastPaymentDate)
		if err == nil {
			// 如果最后处理日期是今天，说明已经处理过了
			if lastProcessDate.Format("2006-01-02") == date.Format("2006-01-02") {
				return true, nil
			}
		}
	}

	return false, nil
}

// updateLastProcessDate 更新最后处理日期
func updateLastProcessDate(ctx context.Context, leaseID string, date time.Time, scope *entities.ResourceScope) error {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	filter := scope.ToFilter()
	filter["_id"] = leaseID

	update := bson.M{
		"$set": bson.M{
			"lastPmtDt": date.Format("2006-01-02"),
		},
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update last process date: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found")
	}

	return nil
}

// updateLeaseBalanceForDueDay 更新租约余额（到期日处理）
func updateLeaseBalanceForDueDay(ctx context.Context, leaseID string, balanceChange float64, scope *entities.ResourceScope) error {
	leaseColl := gomongo.Coll("rr", "leases")
	if leaseColl == nil {
		return fmt.Errorf("leases collection not initialized")
	}

	// 获取当前租约信息
	filter := scope.ToFilter()
	filter["_id"] = leaseID

	var lease entities.Lease
	err := leaseColl.FindOne(ctx, filter).Decode(&lease)
	if err != nil {
		return fmt.Errorf("failed to find lease: %v", err)
	}

	// 计算新余额：当前余额 + 变化量（到期日增加租金）
	// 允许负数余额，表示多付款
	newBalance := lease.OwingBalance + balanceChange

	// 更新租约余额
	update := bson.M{
		"$set": bson.M{
			"owingBal": newBalance,
		},
	}

	result, err := leaseColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lease balance: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("lease not found for balance update")
	}

	return nil
}
