# Rent Report Application - Makefile Usage Guide

## 🚀 Quick Start

### For Development (Recommended)
```bash
# First time setup
make setup

# Start the application (builds frontend + runs backend)
make start
```

### For Docker Deployment
```bash
# Build and run with Docker
make docker-start
```

## 📋 Two Running Methods Explained

### Method 1: Local Development (Your Original Way)
```bash
# Step 1: Build frontend
cd src/web
npm run build

# Step 2: Run backend
cd ../
go run main.go --config local.ini
```

**Makefile equivalent:**
```bash
make start          # Does both steps automatically
# OR
make web-build      # Step 1
make dev-backend    # Step 2
```

### Method 2: Docker Container (Your Alternative Way)
```bash
# Build Docker image
podman build -t rent_report .

# Run Docker container
podman run -p 8089:8089 rent_report
```

**Makefile equivalent:**
```bash
make docker-start   # Does both steps automatically
# OR
make docker-latest  # Build image
make docker-run     # Run container
```

## 🎯 Common Development Workflows

### Daily Development
```bash
make start          # Quick start for development
make dev-full       # Start both frontend dev server + backend
make quick-start    # Fast start without full setup
```

### Testing
```bash
make test           # Run all tests
make test-src       # Run only Go tests in src/
make test-integration # Run tests in tests/ directory
make test-coverage  # Generate coverage report
```

### Building
```bash
make build          # Build for current platform
make build-arm      # Build for ARM64 Linux
make build-all      # Build for all platforms
make full-build     # Build frontend + backend
```

### Docker Operations
```bash
make docker-latest  # Build Docker image
make docker-run     # Run container
make docker-stop    # Stop container
make docker-logs    # View container logs
make docker-shell   # Access container shell
make docker-restart # Restart container
make docker-rebuild # Rebuild and restart
```

### Code Quality
```bash
make lint           # Run code quality checks
make fmt            # Format Go code
make validate       # Run lint + test
make pre-commit     # Pre-commit checks
```

### Utilities
```bash
make clean          # Clean build files
make info           # Show build information
make logs           # Show application logs
make help           # Show all available commands
```

## 🔧 Configuration

### Port Configuration
- **Development**: http://localhost:8089 (configured in `local.ini`)
- **Docker**: http://localhost:8089 (mapped from container)
- **Frontend Dev Server**: http://localhost:5173 (when using `make dev-full`)

### Configuration Files
- `local.ini` - Main configuration file
- `web/package.json` - Frontend dependencies and scripts
- `Dockerfile` - Production container configuration
- `DockerfileArm` - ARM container configuration

## 🎯 Recommended Workflows

### First Time Setup
```bash
make setup          # Install all dependencies and build frontend
make start          # Start the application
```

### Daily Development
```bash
make start          # Standard development mode
# OR
make dev-full       # If you need frontend hot-reload
```

### Before Committing
```bash
make pre-commit     # Run formatting, linting, and tests
```

### Deployment
```bash
make docker-start   # For container deployment
# OR
make deploy-build   # Use existing build.sh script
```

### Testing
```bash
make test           # Run all tests
make test-coverage  # Generate coverage report
```

## 🚨 Important Notes

1. **Frontend must be built first** for the application to serve static files correctly
2. **Configuration file** `local.ini` is required for the application to start
3. **Database connection** is configured in `local.ini` - make sure MongoDB is accessible
4. **Port 8089** is the default port for both development and Docker modes
5. **Static files** are served from `web/dist/` directory

## 🔍 Troubleshooting

### Application won't start
```bash
make info           # Check build information
make logs           # Check application logs
```

### Frontend not loading
```bash
make web-build      # Rebuild frontend assets
```

### Docker issues
```bash
make docker-logs    # Check container logs
make docker-shell   # Access container for debugging
```

### Clean start
```bash
make reset          # Clean everything and setup again
```
