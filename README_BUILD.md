# Rent Report - Build System

This project has been updated with a modern Makefile-based build system that automates the entire build and deployment process.

## 🚀 Quick Start

### Build Everything
```bash
cd src
make full-build
```

This single command will:
- ✅ Install Go and Node.js dependencies
- ✅ Build the frontend (Vite + TailwindCSS)
- ✅ Compile the Go backend
- ✅ Copy all assets to the `build/` directory
- ✅ Copy configuration files
- ✅ Copy documentation

### Build Docker Image
```bash
cd src
make docker-latest
```

This will:
- ✅ Run the full build process
- ✅ Generate a Dockerfile in the build directory
- ✅ Create a Docker image: `rent_report:latest`

## 📁 Build Output Structure

After running `make full-build`, you'll find everything in the `build/` directory:

```
build/
├── Dockerfile              # Generated Docker configuration
├── bin/
│   └── rent_report         # Compiled Go binary
├── web/
│   └── dist/               # Frontend build output
├── configs/                # Configuration files
│   ├── local.ini
│   ├── app.ini
│   └── ...
└── docs/                   # Documentation
    ├── api/
    └── ...
```

## 🛠️ Available Commands

### Build Commands
- `make full-build` - Complete build (frontend + backend + assets)
- `make build` - Build Go backend only
- `make web-build` - Build frontend only
- `make clean` - Clean build directory

### Docker Commands
- `make docker-latest` - Build Docker image with latest tag
- `make docker` - Build Docker image with version tag
- `make create-dockerfile` - Generate Dockerfile only

### Development Commands
- `make dev` - Start development servers (frontend + backend)
- `make web-dev` - Start frontend development server only
- `make test` - Run tests
- `make lint` - Lint and format code

### Utility Commands
- `make help` - Show all available commands
- `make info` - Show build information
- `make deps` - Install dependencies only

## 🐳 Docker Deployment

### Run the Container
```bash
# Run in background
docker run -d --name rent-report \
  -p 8089:8089 \
  -v $(pwd)/configs:/app/configs \
  rent_report:latest

# Run interactively
docker run -it --rm \
  -p 8089:8089 \
  -v $(pwd)/configs:/app/configs \
  rent_report:latest
```

### Access the Application
- **Web Interface**: http://localhost:8089
- **API Documentation**: http://localhost:8089/docs/api/

## 🔧 Configuration

The application uses configuration files from the `configs/` directory:
- `local.ini` - Local development settings
- `app.ini` - Application settings
- `rain.ini` - Production settings

## 📊 Build Features

### ✅ What's Included
- **Automated dependency management** (Go modules + npm)
- **Frontend optimization** (Vite bundling, compression, tree-shaking)
- **Backend compilation** (optimized Go binary with `-ldflags="-w -s"`)
- **Docker containerization** (Alpine Linux, non-root user, health checks)
- **Asset management** (static files, configs, docs)
- **Cross-platform builds** (Linux, macOS, Windows support)

### 🎯 Production Ready
- **Security**: Non-root container user (UID 1000)
- **Performance**: Optimized binary size and startup time
- **Monitoring**: Built-in health checks
- **Scalability**: Stateless container design
- **Compliance**: K8s compatible user permissions

## 🚨 Migration Notes

This new build system replaces the old scripts:
- ❌ `build.sh` (removed)
- ❌ `deploy-static.sh` (removed)
- ❌ Root `package.json` (removed)

All functionality is now consolidated in the Makefile with improved:
- Error handling
- Progress indicators
- Colored output
- Dependency tracking

## 🔍 Troubleshooting

### Build Issues
```bash
# Clean and rebuild
make clean
make full-build

# Check dependencies
make deps

# View build info
make info
```

### Docker Issues
```bash
# Check if Docker is running
docker version

# View container logs
docker logs rent-report

# Debug container
docker exec -it rent-report /bin/sh
```

## 📈 Performance

**Build Times** (approximate):
- Frontend build: ~2-3 seconds
- Backend build: ~1-2 seconds
- Docker image: ~3-5 seconds
- **Total**: ~6-10 seconds

**Image Size**: ~217MB (Alpine Linux + optimized binary)

---

**Need help?** Run `make help` to see all available commands!
